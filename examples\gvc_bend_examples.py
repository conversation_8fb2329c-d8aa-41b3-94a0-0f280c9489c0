"""
Examples demonstrating the General Variable Curvature (GVC) Bend component.

This file shows various use cases and applications of the GVC bend component,
including how to create complex waveguide routing with smooth curvature transitions.
"""

import gdsfactory as gf
import sys
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent.parent))

from components.gvc_bend import gvc_bend
from pdk.layers import LAYER


def example_basic_bends():
    """Demonstrate basic GVC bend usage with different angle and curvature combinations."""
    
    c = gf.Component("basic_gvc_bends")
    
    # Create a series of bends with different parameters
    bends = [
        # (final_angle, final_curvature, label)
        (45.0, 0.0, "45deg straight end"),
        (45.0, 0.05, "45deg left curve end"),
        (45.0, -0.03, "45deg right curve end"),
        (-90.0, 0.0, "-90deg straight end"),
        (30.0, 0.08, "30deg high curve"),
    ]
    
    y_spacing = 50  # Spacing between bends
    
    for i, (angle, curvature, label) in enumerate(bends):
        # Create the bend
        bend = gvc_bend(
            final_angle=angle,
            final_curvature=curvature,
            width=0.5,
            layer=LAYER.WG
        )
        
        # Add to layout
        bend_ref = c << bend
        bend_ref.movey(i * y_spacing)
        
        # Add text label
        text = gf.components.text(
            text=label,
            size=5,
            layer=LAYER.TEXT
        )
        text_ref = c << text
        text_ref.move((bend_ref.xmax + 10, bend_ref.y))
    
    return c


def example_s_curve_routing():
    """Demonstrate S-curve routing using GVC bends."""
    
    c = gf.Component("s_curve_routing")
    
    # Create an S-curve by connecting two GVC bends
    # First bend: turn up and end with right curvature
    bend1 = gvc_bend(
        final_angle=45.0,
        final_curvature=-0.05,  # Right curvature to prepare for S-shape
        width=0.5,
        layer=LAYER.WG
    )
    
    # Second bend: continue the S-curve and end straight
    bend2 = gvc_bend(
        final_angle=-45.0,  # Turn back down
        final_curvature=0.0,  # End straight
        width=0.5,
        layer=LAYER.WG
    )
    
    # Add first bend
    bend1_ref = c << bend1
    
    # Add second bend, connecting to first bend's output
    bend2_ref = c << bend2
    bend2_ref.connect("o1", bend1_ref.ports["o2"])
    
    # Add input and output straight sections
    input_straight = gf.components.straight(
        length=20,
        cross_section=gf.cross_section.strip(width=0.5, layer=LAYER.WG)
    )
    output_straight = gf.components.straight(
        length=20,
        cross_section=gf.cross_section.strip(width=0.5, layer=LAYER.WG)
    )
    
    input_ref = c << input_straight
    output_ref = c << output_straight
    
    input_ref.connect("o2", bend1_ref.ports["o1"])
    output_ref.connect("o1", bend2_ref.ports["o2"])
    
    # Add ports
    c.add_port("input", port=input_ref.ports["o1"])
    c.add_port("output", port=output_ref.ports["o2"])
    
    return c


def example_spiral_approximation():
    """Create a spiral-like structure using multiple GVC bends."""
    
    c = gf.Component("spiral_approximation")
    
    # Parameters for spiral segments
    segments = [
        (90.0, 0.02),   # First quarter turn with slight left curve
        (90.0, 0.04),   # Second quarter turn with more curve
        (90.0, 0.06),   # Third quarter turn with even more curve
        (90.0, 0.08),   # Fourth quarter turn with highest curve
    ]
    
    current_ref = None
    
    for i, (angle, curvature) in enumerate(segments):
        # Create bend segment
        bend = gvc_bend(
            final_angle=angle,
            final_curvature=curvature,
            width=0.5,
            layer=LAYER.WG
        )
        
        bend_ref = c << bend
        
        if current_ref is not None:
            # Connect to previous segment
            bend_ref.connect("o1", current_ref.ports["o2"])
        else:
            # First segment - add input port
            c.add_port("input", port=bend_ref.ports["o1"])
        
        current_ref = bend_ref
    
    # Add output port
    c.add_port("output", port=current_ref.ports["o2"])
    
    return c


def example_custom_routing():
    """Demonstrate custom routing between two points using GVC bends."""
    
    c = gf.Component("custom_routing")
    
    # Define start and end points
    start_point = (0, 0)
    end_point = (100, 60)
    
    # Create a custom route with intermediate waypoints
    # Route: start -> intermediate bend -> final approach
    
    # First segment: turn towards target with some curvature
    bend1 = gvc_bend(
        final_angle=30.0,
        final_curvature=0.03,
        width=0.5,
        layer=LAYER.WG
    )
    
    # Second segment: adjust direction and end straight
    bend2 = gvc_bend(
        final_angle=15.0,
        final_curvature=0.0,
        width=0.5,
        layer=LAYER.WG
    )
    
    # Add bends to layout
    bend1_ref = c << bend1
    bend1_ref.move(start_point)
    
    bend2_ref = c << bend2
    bend2_ref.connect("o1", bend1_ref.ports["o2"])
    
    # Add final straight section to reach target
    final_straight = gf.components.straight(
        length=30,
        cross_section=gf.cross_section.strip(width=0.5, layer=LAYER.WG)
    )
    final_ref = c << final_straight
    final_ref.connect("o1", bend2_ref.ports["o2"])
    
    # Add markers for start and end points
    start_marker = gf.components.cross(length=5, width=0.5, layer=LAYER.MARK)
    end_marker = gf.components.cross(length=5, width=0.5, layer=LAYER.MARK)
    
    start_marker_ref = c << start_marker
    end_marker_ref = c << end_marker
    
    start_marker_ref.move(start_point)
    end_marker_ref.move(end_point)
    
    return c


if __name__ == "__main__":
    print("GVC Bend Examples")
    print("=================")
    
    # Create all examples
    examples = [
        ("Basic Bends", example_basic_bends),
        ("S-Curve Routing", example_s_curve_routing),
        ("Spiral Approximation", example_spiral_approximation),
        ("Custom Routing", example_custom_routing),
    ]
    
    for name, func in examples:
        print(f"\nCreating {name}...")
        try:
            component = func()
            print(f"✓ {name}: {component.xsize:.1f} x {component.ysize:.1f} μm")
            
            # Save as individual GDS file
            filename = name.lower().replace(" ", "_").replace("-", "_")
            component.write_gds(f"build/gds/{filename}.gds")
            print(f"  Saved as: build/gds/{filename}.gds")
            
        except Exception as e:
            print(f"✗ {name}: Error - {e}")
    
    # Create a combined layout with all examples
    print("\nCreating combined layout...")
    combined = gf.Component("gvc_bend_examples_combined")
    
    x_offset = 0
    for name, func in examples:
        try:
            component = func()
            ref = combined << component
            ref.movex(x_offset)
            x_offset += component.xsize + 50  # Add spacing between examples
            
            # Add title
            title = gf.components.text(
                text=name,
                size=8,
                layer=LAYER.TEXT
            )
            title_ref = combined << title
            title_ref.move((ref.x, ref.ymax + 10))
            
        except Exception as e:
            print(f"Skipping {name} in combined layout: {e}")
    
    # Save combined layout
    combined.write_gds("build/gds/gvc_bend_examples_all.gds")
    print(f"✓ Combined layout saved: build/gds/gvc_bend_examples_all.gds")
    
    # Show the combined layout
    print("\nDisplaying combined layout...")
    combined.show()
    
    print("\nAll examples completed!")
