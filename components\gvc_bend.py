"""
General Variable Curvature (GVC) Bend waveguide component.

This module implements a parametric optical waveguide that transitions smoothly 
from a standard horizontal straight waveguide to a user-defined final state 
with controllable trajectory.
"""

import gdsfactory as gf
import numpy as np
from gdsfactory.typings import LayerSpec, CrossSectionSpec
import sys
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent.parent))

from pdk.layers import LAYER


@gf.cell
def gvc_bend(
    final_angle: float = 45.0,
    final_curvature: float = 0.0,
    cross_section: CrossSectionSpec = "strip",
    width: float = 0.5,
    layer: LayerSpec = LAYER.WG,
    npoints: int = 500,
    min_radius: float = 5.0,
) -> gf.Component:
    """General Variable Curvature Bend waveguide component.
    
    Creates a parametric optical waveguide that transitions smoothly from a 
    standard horizontal straight waveguide to a user-defined final state.
    
    Fixed Initial Conditions (Non-negotiable):
    - Starting position: (0, 0) in local coordinate system
    - Starting direction: 0° (positive X-axis direction)  
    - Starting curvature: 0 (perfectly straight for seamless connection)
    
    Args:
        final_angle: Final tangent direction in degrees.
                    Positive values (e.g., +90°): left turn (counter-clockwise)
                    Negative values (e.g., -90°): right turn (clockwise)
        final_curvature: Final curvature in 1/μm units.
                        Positive: curves left, Negative: curves right, Zero: straight
                        Represents 1/radius at the endpoint
        cross_section: Cross-section specification for the waveguide
        width: Width of the waveguide in μm (used if cross_section is "strip")
        layer: Layer specification (used if cross_section is "strip")
        npoints: Number of points for path discretization (higher = smoother)
        min_radius: Minimum radius of curvature in μm (for numerical stability)
        
    Returns:
        gf.Component: The GVC bend component with ports "o1" (input) and "o2" (output)
        
    Raises:
        ValueError: If parameters result in invalid geometry or numerical issues
        
    Examples:
        >>> # 45° left turn ending straight
        >>> bend1 = gvc_bend(final_angle=45.0, final_curvature=0.0)
        
        >>> # 45° left turn ending with left curvature  
        >>> bend2 = gvc_bend(final_angle=45.0, final_curvature=0.1)
        
        >>> # 45° left turn ending with right curvature (S-curve)
        >>> bend3 = gvc_bend(final_angle=45.0, final_curvature=-0.05)
    """
    
    # Parameter validation
    if abs(final_curvature) > 1.0 / min_radius:
        raise ValueError(f"Final curvature magnitude ({abs(final_curvature):.3f}) exceeds "
                        f"maximum allowed ({1.0/min_radius:.3f}) for min_radius={min_radius}μm")
    
    if npoints < 10:
        raise ValueError("npoints must be at least 10 for proper discretization")
        
    # Convert angle to radians
    theta_final = np.deg2rad(final_angle)
    kappa_final = final_curvature
    
    # Handle special cases
    if abs(theta_final) < 1e-6 and abs(kappa_final) < 1e-6:
        # Straight waveguide case
        return _create_straight_waveguide(cross_section, width, layer, 10.0)
    
    if abs(theta_final) < 1e-6:
        # Zero angle but non-zero curvature: create a symmetric curve
        return _create_symmetric_curve(kappa_final, cross_section, width, layer, npoints, min_radius)
    
    # Calculate path geometry
    path_length, s_array, kappa_array = _calculate_path_geometry(
        theta_final, kappa_final, npoints, min_radius
    )
    
    # Calculate path coordinates
    x_array, y_array, theta_array = _integrate_path(s_array, kappa_array)
    
    # Create the component
    c = gf.Component("gvc_bend")
    
    # Create path points
    points = np.column_stack((x_array, y_array))
    
    # Create gdsfactory path
    path = gf.Path()
    path.points = points
    
    # Handle cross-section
    if cross_section == "strip":
        xs = gf.cross_section.strip(width=width, layer=layer)
    else:
        xs = cross_section
    
    # Extrude the path
    waveguide_ref = c << path.extrude(cross_section=xs)

    # Add ports using the waveguide reference ports (which are properly calculated)
    # This ensures the ports are correctly oriented rectangles
    c.add_ports(waveguide_ref.ports)
    
    return c


def _calculate_path_geometry(theta_final, kappa_final, npoints, min_radius):
    """Calculate the path geometry for the GVC bend using improved mathematical model."""

    if abs(kappa_final) < 1e-9:
        # Final curvature is essentially zero - use symmetric profile
        # Create a curve that goes up and comes back down to achieve the angle

        # For symmetric sine profile: ∫₀¹ sin(πt) dt = 2/π
        # So: θ_final = max_kappa * L * (2/π)
        # Therefore: L = θ_final * π / (2 * max_kappa)

        # Choose max_kappa to give reasonable path length
        target_length = max(abs(theta_final) * 20, 2.0 * min_radius)  # Heuristic
        max_kappa = abs(theta_final) * np.pi / (2.0 * target_length)

        # Ensure max_kappa doesn't exceed limits
        if max_kappa > 1.0 / min_radius:
            max_kappa = 1.0 / min_radius
            target_length = abs(theta_final) * np.pi / (2.0 * max_kappa)

        # Apply sign to match desired turn direction
        max_kappa = np.sign(theta_final) * max_kappa
        path_length = target_length

        s_array = np.linspace(0, path_length, npoints)
        t_array = s_array / path_length

        # Symmetric curvature profile: κ(t) = max_kappa * sin(πt)
        kappa_array = max_kappa * np.sin(np.pi * t_array)

    else:
        # Non-zero final curvature - use improved smooth transition
        # Use a more sophisticated approach to match both angle and final curvature

        # For smoothstep profile: ∫₀¹ (3t² - 2t³) dt = 1/2
        # So: θ_final = kappa_final * L * (1/2)
        # Therefore: L = 2 * θ_final / kappa_final

        if abs(kappa_final) > 1e-9:
            path_length = 2.0 * abs(theta_final) / abs(kappa_final)
        else:
            path_length = 10.0  # Fallback

        # Ensure reasonable path length bounds
        path_length = max(path_length, 2.0 * min_radius)
        path_length = min(path_length, 1000.0)  # Reasonable upper limit

        s_array = np.linspace(0, path_length, npoints)
        t_array = s_array / path_length

        # Smooth curvature transition using smoothstep function
        # κ(t) = κ_final * (3t² - 2t³)
        smoothstep = 3 * t_array**2 - 2 * t_array**3
        kappa_array = kappa_final * smoothstep

    return path_length, s_array, kappa_array


def _integrate_path(s_array, kappa_array):
    """Integrate curvature to get angles and positions using improved numerical methods."""

    # Use trapezoidal rule for more accurate integration
    # Integrate curvature to get angles: θ(s) = ∫₀ˢ κ(s') ds'
    theta_array = np.zeros_like(s_array)
    theta_array[1:] = np.cumsum(
        0.5 * (kappa_array[:-1] + kappa_array[1:]) * np.diff(s_array)
    )

    # Integrate angles to get positions using trapezoidal rule
    # x(s) = ∫₀ˢ cos(θ(s')) ds', y(s) = ∫₀ˢ sin(θ(s')) ds'
    cos_theta = np.cos(theta_array)
    sin_theta = np.sin(theta_array)

    x_array = np.zeros_like(s_array)
    y_array = np.zeros_like(s_array)

    x_array[1:] = np.cumsum(
        0.5 * (cos_theta[:-1] + cos_theta[1:]) * np.diff(s_array)
    )
    y_array[1:] = np.cumsum(
        0.5 * (sin_theta[:-1] + sin_theta[1:]) * np.diff(s_array)
    )

    return x_array, y_array, theta_array


def _create_straight_waveguide(cross_section, width, layer, length):
    """Create a straight waveguide for the trivial case."""
    if cross_section == "strip":
        xs = gf.cross_section.strip(width=width, layer=layer)
    else:
        xs = cross_section
        
    return gf.components.straight(length=length, cross_section=xs)


def _create_symmetric_curve(kappa_final, cross_section, width, layer, npoints, min_radius):
    """Create a symmetric curve that ends straight but with specified curvature.

    This is a complex case where we want zero final angle but non-zero final curvature.
    For now, we return a straight waveguide as this case requires more sophisticated
    mathematical treatment.
    """
    # Suppress unused parameter warnings - these are kept for future implementation
    _ = kappa_final, npoints, min_radius

    # TODO: Implement proper symmetric curve that achieves zero angle with final curvature
    # This would require solving a more complex boundary value problem
    return _create_straight_waveguide(cross_section, width, layer, 10.0)


def _verify_bend_accuracy(component, target_angle, target_curvature, tolerance=1.0):
    """Verify that the bend achieves the target angle and curvature within tolerance."""
    # Suppress unused parameter warning - kept for future curvature verification
    _ = target_curvature

    ports = component.ports
    if len(ports) < 2:
        return False, "Insufficient ports"

    # Get port positions to calculate actual path angle
    input_port = ports["o1"]
    output_port = ports["o2"]

    # Calculate the actual angle from input to output position
    dx = output_port.center[0] - input_port.center[0]
    dy = output_port.center[1] - input_port.center[1]

    if abs(dx) < 1e-6 and abs(dy) < 1e-6:
        return False, "Input and output ports are at the same position"

    # Calculate angle from position difference
    actual_angle = np.degrees(np.arctan2(dy, dx))

    # Calculate angle error
    angle_error = abs(actual_angle - target_angle)

    # Handle angle wrapping (e.g., -179° vs 181°)
    if angle_error > 180:
        angle_error = 360 - angle_error

    angle_ok = angle_error < tolerance

    return angle_ok, f"Angle error: {angle_error:.2f}° (actual: {actual_angle:.1f}°, target: {target_angle:.1f}°)"


if __name__ == "__main__":
    print("Testing GVC Bend component...")

    # Test case 1: 45° left turn ending straight
    print("\n=== Test 1: 45° left turn ending straight ===")
    bend1 = gvc_bend(final_angle=45.0, final_curvature=0.0)
    print(f"Component size: {bend1.xsize:.1f} x {bend1.ysize:.1f} μm")
    bend1.pprint_ports()

    # Test case 2: 45° left turn ending with left curvature
    print("\n=== Test 2: 45° left turn ending with left curvature ===")
    bend2 = gvc_bend(final_angle=45.0, final_curvature=0.05)
    print(f"Component size: {bend2.xsize:.1f} x {bend2.ysize:.1f} μm")
    bend2.pprint_ports()

    # Test case 3: 45° left turn ending with right curvature (S-curve)
    print("\n=== Test 3: 45° left turn ending with right curvature ===")
    bend3 = gvc_bend(final_angle=45.0, final_curvature=-0.02)
    print(f"Component size: {bend3.xsize:.1f} x {bend3.ysize:.1f} μm")
    bend3.pprint_ports()

    # Test case 4: Right turn (negative angle)
    print("\n=== Test 4: -90° right turn ending straight ===")
    bend4 = gvc_bend(final_angle=-90.0, final_curvature=0.0)
    print(f"Component size: {bend4.xsize:.1f} x {bend4.ysize:.1f} μm")
    bend4.pprint_ports()

    # Test case 5: Small angle with high curvature
    print("\n=== Test 5: 15° turn with high final curvature ===")
    bend5 = gvc_bend(final_angle=15.0, final_curvature=0.1)
    print(f"Component size: {bend5.xsize:.1f} x {bend5.ysize:.1f} μm")
    bend5.pprint_ports()

    # Verify accuracy for some test cases
    print("\n=== Accuracy Verification ===")
    for i, (bend, target_angle, name) in enumerate([
        (bend1, 45.0, "Test 1"),
        (bend4, -90.0, "Test 4"),
        (bend5, 15.0, "Test 5")
    ], 1):
        ok, msg = _verify_bend_accuracy(bend, target_angle, 0.0)
        status = "✓" if ok else "✗"
        print(f"{status} {name}: {msg}")

    # Show one of the components
    print("\nDisplaying Test 2 component...")
    bend2.show()

    print("\nTesting complete!")
