# General Variable Curvature (GVC) Bend Component

## Overview

The General Variable Curvature (GVC) Bend is a parametric optical waveguide component that creates smooth transitions from a standard horizontal straight waveguide to a user-defined final state with controllable trajectory. This component provides a "black box" tool where users specify "where to go" (final state) and "how to get there" (trajectory control).

## Key Features

### Fixed Initial Conditions (Non-negotiable)
- **Starting position**: (0, 0) in local coordinate system
- **Starting direction**: 0° (positive X-axis direction)
- **Starting curvature**: 0 (perfectly straight for seamless connection)

### User-Controllable Parameters
- **`final_angle`** (float): Final tangent direction in degrees
  - Positive values (e.g., +90°): left turn (counter-clockwise)
  - Negative values (e.g., -90°): right turn (clockwise)
- **`final_curvature`** (float): Final curvature in 1/μm units
  - Positive: curves left
  - Negative: curves right
  - Zero: straight
  - Represents 1/radius at the endpoint

## Usage

### Basic Import and Setup

```python
import gdsfactory as gf
from components.gvc_bend import gvc_bend
from pdk.layers import LAYER
```

### Simple Examples

#### 1. 45° Left Turn Ending Straight
```python
bend = gvc_bend(
    final_angle=45.0,
    final_curvature=0.0,
    width=0.5,
    layer=LAYER.WG
)
```

#### 2. 45° Left Turn Ending with Left Curvature
```python
bend = gvc_bend(
    final_angle=45.0,
    final_curvature=0.05,  # Positive = left curve
    width=0.5,
    layer=LAYER.WG
)
```

#### 3. 45° Left Turn Ending with Right Curvature (S-curve)
```python
bend = gvc_bend(
    final_angle=45.0,
    final_curvature=-0.03,  # Negative = right curve
    width=0.5,
    layer=LAYER.WG
)
```

#### 4. 90° Right Turn
```python
bend = gvc_bend(
    final_angle=-90.0,  # Negative = right turn
    final_curvature=0.0,
    width=0.5,
    layer=LAYER.WG
)
```

### Advanced Usage

#### Custom Cross-Section
```python
# Create custom cross-section
xs = gf.cross_section.strip(width=1.0, layer=LAYER.WG)

bend = gvc_bend(
    final_angle=60.0,
    final_curvature=0.02,
    cross_section=xs
)
```

#### High-Precision Bend
```python
bend = gvc_bend(
    final_angle=30.0,
    final_curvature=0.01,
    npoints=1000,  # Higher resolution
    min_radius=10.0,  # Larger minimum radius
    width=0.5,
    layer=LAYER.WG
)
```

## Parameters Reference

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `final_angle` | float | 45.0 | Final tangent direction in degrees |
| `final_curvature` | float | 0.0 | Final curvature in 1/μm units |
| `cross_section` | CrossSectionSpec | "strip" | Cross-section specification |
| `width` | float | 0.5 | Waveguide width in μm (if cross_section="strip") |
| `layer` | LayerSpec | LAYER.WG | Layer specification (if cross_section="strip") |
| `npoints` | int | 500 | Number of points for path discretization |
| `min_radius` | float | 5.0 | Minimum radius of curvature in μm |

## Mathematical Model

The GVC bend uses a sophisticated mathematical model that:

1. **For zero final curvature**: Uses a symmetric sinusoidal curvature profile
   - κ(t) = κ_max × sin(πt)
   - Ensures smooth start and end with zero curvature

2. **For non-zero final curvature**: Uses a smoothstep transition function
   - κ(t) = κ_final × (3t² - 2t³)
   - Provides smooth transition from zero to final curvature

3. **Path integration**: Uses trapezoidal rule for accurate numerical integration
   - Angle: θ(s) = ∫₀ˢ κ(s') ds'
   - Position: x(s) = ∫₀ˢ cos(θ(s')) ds', y(s) = ∫₀ˢ sin(θ(s')) ds'

## Design Philosophy

The GVC bend is designed as a "black box" tool where:
- Users specify the **desired outcome** (final angle and curvature)
- The component automatically calculates the **path geometry** (total length, intermediate points)
- **Smooth optical transitions** minimize mode mismatch and optical losses
- **Parametric control** allows fine-tuning of the trajectory

## Ports

The component provides two optical ports:
- **`o1`** (input): Located at (0,0), orientation 180° (faces left for rightward propagation)
- **`o2`** (output): Located at calculated end position, orientation matches final angle + 180°

## Examples and Applications

See `examples/gvc_bend_examples.py` for comprehensive examples including:
- Basic bend variations
- S-curve routing
- Spiral approximations
- Custom routing between points

## Limitations and Considerations

1. **Parameter bounds**: Final curvature is limited by `min_radius` parameter
2. **Numerical accuracy**: Very small angles with high curvatures may have reduced accuracy
3. **Path length**: Automatically calculated but may be large for certain parameter combinations
4. **Fabrication constraints**: Consider your fabrication process limits for minimum radius

## Testing

Run the component tests:
```bash
python components/gvc_bend.py
```

Run the examples:
```bash
python examples/gvc_bend_examples.py
```

## Future Enhancements

- Improved mathematical model for edge cases
- Support for asymmetric curvature profiles
- Integration with automatic routing algorithms
- Optimization for specific optical performance metrics
