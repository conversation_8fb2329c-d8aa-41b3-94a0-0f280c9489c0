import gdsfactory as gf
section_inner = gf.cross_section.cross_section(
    width=1.5,
    offset=0,
    layer=(1, 0)
)
section_outer = gf.cross_section.cross_section(
    width=2,
    offset=0,
    layer=(1, 0)
)

# 创建基础环形耦合器
coupler = gf.components.coupler_ring_bend(
    coupler_gap=0.5,
    radius=50,
    coupling_angle_coverage=20,
    length_x=0,
    cross_section_inner=section_inner,
    cross_section_outer=section_outer,
    bend_output="bend_euler",
    straight="straight",
)

# 创建新的组件
c = gf.Component()

# 添加耦合器
coupler_ref = c << coupler

# 添加两个直波导
s1 = c << gf.get_component("straight", length=20, cross_section=section_outer)
s2 = c << gf.get_component("straight", length=20, cross_section=section_outer)

# 连接直波导到耦合器的端口
s1.connect("o1", coupler_ref.ports["o1"])
s2.connect("o1", coupler_ref.ports["o4"])

# 添加端口（新的端口位置）
c.add_port("o1", port=s1.ports["o2"])
c.add_port("o2", port=coupler_ref.ports["o2"])
c.add_port("o3", port=coupler_ref.ports["o3"])
c.add_port("o4", port=s2.ports["o2"])

c.pprint_ports()
c.draw_ports()
c.plot()
c.show()

c = gf.Component()
    # 这里是波导（1，0）
    #WG = gf.components.bends.bend_euler_s(layer=(1,0))
    #WG=bend_s_offset(offset=offset, radius=radius)
    #WG=gf.components.crossing_etched(width=0.8, r1=r1, r2=r2, w=w, L=3.5, layer_wg='WG')
    #xs = gf.cross_section.strip(width=1, layer=(1, 0))
    # WG=gf.components.straight(
    # length=10,  # Length in microns
    # cross_section=xs
    # )       
section_inner = gf.cross_section.cross_section(
width=1.5,
offset=0,
layer=(1, 0)
)
section_outer = gf.cross_section.cross_section(
    width=2,
    offset=0,
    layer=(1, 0)
)

# 创建基础环形耦合器
coupler = gf.components.coupler_ring_bend(
    coupler_gap=0.5,
    radius=50,
    coupling_angle_coverage=20,
    length_x=0,
    cross_section_inner=section_inner,
    cross_section_outer=section_outer,
    bend_output="bend_euler",
    straight="straight",
)

# 创建新的组件
WG = gf.Component()

# 添加耦合器
coupler_ref = WG << coupler

# 添加两个直波导
s1 = WG << gf.get_component("straight", length=20, cross_section=section_outer)
s2 = WG << gf.get_component("straight", length=20, cross_section=section_outer)

# 连接直波导到耦合器的端口
s1.connect("o1", coupler_ref.ports["o1"])
s2.connect("o1", coupler_ref.ports["o4"])

# 添加端口（新的端口位置）
WG.add_port("o1", port=s1.ports["o2"])
# WG.add_port("o2", port=coupler_ref.ports["o2"])

WG.add_port("o3", port=coupler_ref.ports["o3"])
# WG.add_port("o4", port=s2.ports["o2"])
# Add the MMI component as a reference to our main component `c`.
WG_ref = c.add_ref(WG)
# Create a rectangle background with the same size as the MMI.
# The .size attribute is a convenient way to get the (width, height) tuple.
box = gf.components.rectangle(
    size=(WG_ref.xmax - WG_ref.xmin+3, WG_ref.ymax - WG_ref.ymin+6), layer=(3,0)
)
slab= gf.components.rectangle(
    size=(WG_ref.xmax - WG_ref.xmin+3, WG_ref.ymax - WG_ref.ymin+6), layer=(2,0)
)
clad=gf.components.rectangle(
    size=(WG_ref.xmax - WG_ref.xmin+3, WG_ref.ymax - WG_ref.ymin+6), layer=(5,0)
)
# Add the background rectangle as a reference to `c`.
rect_ref = c.add_ref(box)
slab_ref = c.add_ref(slab) 
clad_ref = c.add_ref(clad)
# Align the center of the rectangle with the center of the MMI.
WG_ref.center = (0, 0)
rect_ref.center = WG_ref.center
slab_ref.center = WG_ref.center
clad_ref.center = WG_ref.center
# Add the optical ports from the MMI reference to the main component `c`.
c.add_ports(WG_ref.ports)
c.plot()
c.draw_ports()

import gdsfactory as gf
# define a custom cross-section
custom_in = gf.cross_section.cross_section(
    width=1.5,
    offset=0,
    layer=(1, 0)
)
custom_out = gf.cross_section.cross_section(
    width=2,
    offset=0,
    layer=(1, 0)
)
c = gf.components.ring_single_bend_coupler(
    radius=50,
    gap=2,
    coupling_angle_coverage=120,
    bend="bend_circular",
    bend_output="bend_euler",
    length_x=0,
    length_y=0,
    cross_section_inner=custom_in,
    cross_section_outer=custom_out
).copy()
c.draw_ports()
c.plot()

import gdsfactory as gf
c = gf.Component()
custom_in = gf.cross_section.cross_section(
    width=1.5,
    offset=0,
    layer=(1, 0)
)
cp = gf.component.coupler_bend(
        radius=10,
        coupler_gap=2,
        coupling_angle_coverage=180,
        cross_section_inner=custom_in,
        cross_section_outer=custom_in,
       
    )
cp.show()
sin = gf.get_component(straight, length=length_x, cross_section=cross_section_inner)
sout = gf.get_component(
        straight, length=length_x, cross_section=cross_section_outer
    )

coupler_right = c << cp
coupler_left = c << cp
straight_inner = c << sin
straight_inner.movex(-length_x / 2)
straight_outer = c << sout
straight_outer.movex(-length_x / 2)

coupler_left.connect("o1", straight_outer.ports["o1"])
straight_inner.connect("o1", coupler_left.ports["o2"])
coupler_right.connect("o2", straight_inner.ports["o2"], mirror=True)
straight_outer.connect("o2", coupler_right.ports["o1"])

c.add_port("o1", port=coupler_left.ports["o3"])
c.add_port("o2", port=coupler_left.ports["o4"])
c.add_port("o4", port=coupler_right.ports["o3"])
c.add_port("o3", port=coupler_right.ports["o4"])
    # c.flatten()
  

@gf.cell_with_module_name
def coupler_ring_bend(
    radius: float | None = None,
    coupler_gap: float = 0.2,
    coupling_angle_coverage: float = 90.0,
    length_x: float = 0.0,
    cross_section_inner: CrossSectionSpec = "strip",
    cross_section_outer: CrossSectionSpec = "strip",
    bend: AnyComponentFactory = bend_circular_all_angle,
    bend_output: ComponentSpec = "bend_euler",
    straight: ComponentSpec = "straight",
) -> Component:
    r"""Two back-to-back coupler_bend.

    Args:
        radius: um. Default is None, which uses the default radius of the cross_section.
        coupler_gap: um.
        angle_inner: of the inner bend, from beginning to end. Depending on the bend chosen, gap may not be preserved.
        angle_outer: of the outer bend, from beginning to end. Depending on the bend chosen, gap may not be preserved.
        coupling_angle_coverage: degrees.
        length_x: horizontal straight length.
        cross_section_inner: spec inner bend.
        cross_section_outer: spec outer bend.
        bend: for bend.
        bend_output: for bend.
        straight: for straight.
    """
    c = Component()
    cp = coupler_bend(
        radius=radius,
        coupler_gap=coupler_gap,
        coupling_angle_coverage=coupling_angle_coverage,
        cross_section_inner=cross_section_inner,
        cross_section_outer=cross_section_outer,
        bend=bend,
        bend_output=bend_output,
    )
    sin = gf.get_component(straight, length=length_x, cross_section=cross_section_inner)
    sout = gf.get_component(
        straight, length=length_x, cross_section=cross_section_outer
    )

    coupler_right = c << cp
    coupler_left = c << cp
    straight_inner = c << sin
    straight_inner.movex(-length_x / 2)
    straight_outer = c << sout
    straight_outer.movex(-length_x / 2)

    coupler_left.connect("o1", straight_outer.ports["o1"])
    straight_inner.connect("o1", coupler_left.ports["o2"])
    coupler_right.connect("o2", straight_inner.ports["o2"], mirror=True)
    straight_outer.connect("o2", coupler_right.ports["o1"])

    c.add_port("o1", port=coupler_left.ports["o3"])
    c.add_port("o2", port=coupler_left.ports["o4"])
    c.add_port("o4", port=coupler_right.ports["o3"])
    c.add_port("o3", port=coupler_right.ports["o4"])
    # c.flatten()
    return c

import gdsfactory as gf

c = gf.components.coupler_bent(gap=0.2, radius=26, length=5, width1=0.4, width2=0.4, length_straight=0, cross_section='strip').copy()
c.draw_ports()
c.plot()

import numpy as np

import gdsfactory as gf


@gf.cell_with_module_name
def coupler_bent_half(
    gap: float = 0.200,
    radius: float = 26,
    length: float = 0,
    width1: float = 0.400,
    width2: float = 0.400,
    length_straight: float = 0,
    length_straight_exit: float = 0,
    cross_section: str = "strip",
) -> gf.Component:
    """Returns Broadband SOI curved / straight directional coupler.

    Args:
        gap: gap.
        radius: radius coupling.
        length: coupler_length.
        width1: width1.
        width2: width2.
        length_straight: input and output straight length.
        length_straight_exit: length straight exit.
        cross_section: cross_section.
    """
    radius_outer = radius + (width1 + gap) / 2
    radius_inner = radius - (width2 + gap) / 2
    alpha = round(np.rad2deg(length / (2 * radius)), 4)
    beta = alpha

    c = gf.Component()

    xs = gf.get_cross_section(cross_section)
    xs1 = xs.copy(radius=radius_outer, width=width1)
    xs2 = xs.copy(radius=radius_inner, width=width2)

    outer_bend = gf.path.arc(angle=-alpha, radius=radius_outer)
    inner_bend = gf.path.arc(angle=-alpha, radius=radius_inner)

    outer_straight = gf.path.straight(length=length, npoints=100)
    inner_straight = gf.path.straight(length=length, npoints=100)

    outer_exit_bend = gf.path.arc(angle=alpha, radius=radius_outer)
    inner_exit_bend_down = gf.path.arc(angle=-beta, radius=radius_inner)
    inner_exit_bend_up = gf.path.arc(angle=alpha + beta, radius=radius_inner)

    inner_exit_straight = gf.path.straight(
        length=length_straight,
        npoints=100,
    )
    outer_exit_straight = gf.path.straight(
        length=length_straight_exit,
        npoints=100,
    )

    outer = outer_bend + outer_straight + outer_exit_bend + outer_exit_straight
    inner = (
        inner_bend
        + inner_straight
        + inner_exit_bend_down
        + inner_exit_bend_up
        + inner_exit_straight
    )

    inner_component = c << inner.extrude(xs2)
    outer_component = c << outer.extrude(xs1)
    outer_component.movey(+(width1 + gap) / 2)
    inner_component.movey(-(width2 + gap) / 2)

    c.add_port("o1", port=outer_component.ports["o1"])
    c.add_port("o2", port=inner_component.ports["o1"])
    c.add_port("o3", port=outer_component.ports["o2"])
    c.add_port("o4", port=inner_component.ports["o2"])
    c.flatten()
    return c


@gf.cell_with_module_name
def coupler_bent(
    gap: float = 0.200,
    radius: float = 26,
    length: float = 8.6,
    width1: float = 0.400,
    width2: float = 0.400,
    length_straight: float = 10,
    cross_section: str = "strip",
) -> gf.Component:
    """Returns Broadband SOI curved / straight directional coupler.

    based on: https://doi.org/10.1038/s41598-017-07618-6.

    Args:
        gap: gap.
        radius: radius coupling.
        length: coupler_length.
        width1: width1.
        width2: width2.
        length_straight: input and output straight length.
        cross_section: cross_section.
    """
    c = gf.Component()

    right_half = c << coupler_bent_half(
        gap=gap,
        radius=radius,
        length=length,
        width1=width1,
        width2=width2,
        length_straight=length_straight,
        cross_section=cross_section,
    )
    left_half = c << coupler_bent_half(
        gap=gap,
        radius=radius,
        length=length,
        width1=width1,
        width2=width2,
        length_straight=length_straight,
        cross_section=cross_section,
    )

    left_half.connect(port="o1", other=right_half.ports["o1"], mirror=True)

    c.add_port("o1", port=left_half.ports["o3"])
    c.add_port("o2", port=left_half.ports["o4"])
    c.add_port("o3", port=right_half.ports["o3"])
    c.add_port("o4", port=right_half.ports["o4"])

    c.flatten()
    return c


if __name__ == "__main__":
    c = coupler_bent_half()
    # c = coupler_bent_half()
    # c = coupler_bent()
    c.plot()


from __future__ import annotations

import gdsfactory as gf
from gdsfactory.component import Component
from gdsfactory.components.couplers.coupler import coupler_straight
from gdsfactory.components.couplers.coupler90 import coupler90
from gdsfactory.typings import ComponentSpec, CrossSectionSpec


@gf.cell_with_module_name
def coupler_ring(
    gap: float = 0.2,
    radius: float = 5.0,
    length_x: float = 4.0,
    bend: ComponentSpec = "bend_euler",
    straight: ComponentSpec = "straight",
    cross_section: CrossSectionSpec = "strip",
    cross_section_bend: CrossSectionSpec | None = None,
    length_extension: float = 3.0,
) -> Component:
    r"""Coupler for ring.

    Args:
        gap: spacing between parallel coupled straight waveguides.
        radius: of the bends.
        length_x: length of the parallel coupled straight waveguides.
        bend: 90 degrees bend spec.
        straight: straight spec.
        cross_section: cross_section spec.
        cross_section_bend: optional bend cross_section spec.
        length_extension: straight length extension at the end of the coupler bottom ports.

    .. code::

          o2            o3
           |             |
            \           /
             \         /
           ---=========---
        o1    length_x   o4

          o2                              o3
          xx                              xx
          xx                             xx
           xx          length_x          x
            xx     ◄───────────────►    x
             xx                       xxx
               xx                   xxx
                xxx──────▲─────────xxx
                         │gap
                 o1──────▼─────────◄──────────────► o4
                                    length_extension
    """
    if length_extension is None:
        length_extension = 3 + radius

    c = Component()
    gap = gf.snap.snap_to_grid(gap, grid_factor=2)
    cross_section_bend = cross_section_bend or cross_section

    # define subcells
    coupler90_component = gf.get_component(
        coupler90,
        gap=gap,
        radius=radius,
        bend=bend,
        straight=straight,
        cross_section=cross_section,
        cross_section_bend=cross_section_bend,
        length_straight=length_extension,
    )
    coupler_straight_component = gf.get_component(
        coupler_straight,
        gap=gap,
        length=length_x,
        cross_section=cross_section,
    )

    # add references to subcells
    cbl = c << coupler90_component
    cbr = c << coupler90_component
    cs = c << coupler_straight_component

    # connect references
    cs.connect(port="o4", other=cbr.ports["o1"])
    cbl.connect(port="o2", other=cs.ports["o2"], mirror=True)

    c.add_port("o1", port=cbl.ports["o4"])
    c.add_port("o2", port=cbl.ports["o3"])
    c.add_port("o3", port=cbr.ports["o3"])
    c.add_port("o4", port=cbr.ports["o4"])

    c.add_ports(
        gf.port.select_ports_list(ports=cbl.ports, port_type="electrical"), prefix="cbl"
    )
    c.add_ports(
        gf.port.select_ports_list(ports=cbr.ports, port_type="electrical"), prefix="cbr"
    )
    c.auto_rename_ports()
    c.flatten()
    return c


if __name__ == "__main__":
    c = coupler_ring()
    c.plot()


from __future__ import annotations

from typing import Any

import gdsfactory as gf
from gdsfactory.component import Component
from gdsfactory.components.bends.bend_circular import bend_circular_all_angle
from gdsfactory.typings import AnyComponentFactory, ComponentSpec, CrossSectionSpec


@gf.cell_with_module_name
def coupler_bend(
    radius: float = 10.0,
    coupler_gap: float = 0.2,
    coupling_angle_coverage: float = 120.0,
    cross_section_inner: CrossSectionSpec = "strip",
    cross_section_outer: CrossSectionSpec = "strip",
    bend: AnyComponentFactory = bend_circular_all_angle,
    bend_output: ComponentSpec = "bend_euler",
) -> Component:
    r"""Compact curved coupler with bezier escape.

    TODO: fix for euler bends.

    Args:
        radius: um.
        coupler_gap: um.
        coupling_angle_coverage: degrees.
        cross_section_inner: spec inner bend.
        cross_section_outer: spec outer bend.
        bend: for bend.
        bend_output: for bend.

    .. code::

            r   4
            |   |
            |  / ___3
            | / /
        2____/ /
        1_____/
    """
    c = Component()

    xi = gf.get_cross_section(cross_section_inner)
    xo = gf.get_cross_section(cross_section_outer)

    angle_inner = 90
    angle_outer = coupling_angle_coverage / 2
    gap = coupler_gap

    width = xo.width / 2 + xi.width / 2
    spacing = gap + width

    bend90_inner_right = gf.get_component(
        bend,  # type: ignore[arg-type]
        radius=radius,
        cross_section=cross_section_inner,
        angle=angle_inner,
    )
    bend_output_right = gf.get_component(
        bend,  # type: ignore[arg-type]
        radius=radius + spacing,
        cross_section=cross_section_outer,
        angle=angle_outer,
    )
    bend_inner_ref = c.create_vinst(bend90_inner_right)
    bend_output_ref = c.create_vinst(bend_output_right)

    output = gf.get_component(
        bend_output, angle=angle_outer, cross_section=cross_section_outer
    )
    output_ref = c.create_vinst(output)
    output_ref.connect("o1", bend_output_ref.ports["o2"], mirror=True)

    pbw = bend_inner_ref.ports["o1"]
    bend_inner_ref.movey(pbw.center[1] + spacing)

    c.add_port("o1", port=bend_output_ref.ports["o1"])
    c.add_port("o2", port=bend_inner_ref.ports["o1"])
    c.add_port("o3", port=output_ref.ports["o2"])
    c.add_port("o4", port=bend_inner_ref.ports["o2"])
    return c


@gf.cell_with_module_name
def coupler_ring_bend(
    radius: float = 10.0,
    coupler_gap: float = 0.2,
    coupling_angle_coverage: float = 90.0,
    length_x: float = 0.0,
    cross_section_inner: CrossSectionSpec = "strip",
    cross_section_outer: CrossSectionSpec = "strip",
    bend: AnyComponentFactory = bend_circular_all_angle,
    bend_output: ComponentSpec = "bend_euler",
    straight: ComponentSpec = "straight",
) -> Component:
    r"""Two back-to-back coupler_bend.

    Args:
        radius: um.
        coupler_gap: um.
        angle_inner: of the inner bend, from beginning to end. Depending on the bend chosen, gap may not be preserved.
        angle_outer: of the outer bend, from beginning to end. Depending on the bend chosen, gap may not be preserved.
        coupling_angle_coverage: degrees.
        length_x: horizontal straight length.
        cross_section_inner: spec inner bend.
        cross_section_outer: spec outer bend.
        bend: for bend.
        bend_output: for bend.
        straight: for straight.
    """
    c = Component()
    cp = coupler_bend(
        radius=radius,
        coupler_gap=coupler_gap,
        coupling_angle_coverage=coupling_angle_coverage,
        cross_section_inner=cross_section_inner,
        cross_section_outer=cross_section_outer,
        bend=bend,
        bend_output=bend_output,
    )
    sin = gf.get_component(straight, length=length_x, cross_section=cross_section_inner)
    sout = gf.get_component(
        straight, length=length_x, cross_section=cross_section_outer
    )

    coupler_right = c << cp
    coupler_left = c << cp
    straight_inner = c << sin
    straight_inner.movex(-length_x / 2)
    straight_outer = c << sout
    straight_outer.movex(-length_x / 2)

    coupler_left.connect("o1", straight_outer.ports["o1"])
    straight_inner.connect("o1", coupler_left.ports["o2"])
    coupler_right.connect("o2", straight_inner.ports["o2"], mirror=True)
    straight_outer.connect("o2", coupler_right.ports["o1"])

    c.add_port("o1", port=coupler_left.ports["o3"])
    c.add_port("o2", port=coupler_left.ports["o4"])
    c.add_port("o4", port=coupler_right.ports["o3"])
    c.add_port("o3", port=coupler_right.ports["o4"])
    # c.flatten()
    return c


@gf.cell_with_module_name
def ring_single_bend_coupler(
    radius: float = 5.0,
    gap: float = 0.2,
    coupling_angle_coverage: float = 90.0,
    bend_all_angle: AnyComponentFactory = bend_circular_all_angle,
    bend: ComponentSpec = "bend_circular",
    bend_output: ComponentSpec = "bend_euler",
    length_x: float = 0.6,
    length_y: float = 0.6,
    cross_section_inner: CrossSectionSpec = "strip",
    cross_section_outer: CrossSectionSpec = "strip",
    **kwargs: Any,
) -> Component:
    r"""Returns ring with curved coupler.

    TODO: enable euler bends.

    Args:
        radius: um.
        gap: um.
        coupling_angle_coverage: degrees.
        angle_inner: of the inner bend, from beginning to end. Depending on the bend chosen, gap may not be preserved.
        angle_outer: of the outer bend, from beginning to end. Depending on the bend chosen, gap may not be preserved.
        bend_all_angle: for bend.
        bend: for bend.
        bend_output: for bend.
        length_x: horizontal straight length.
        length_y: vertical straight length.
        cross_section_inner: spec inner bend.
        cross_section_outer: spec outer bend.
        kwargs: cross_section settings.
    """
    c = Component()

    coupler = coupler_ring_bend(
        radius=radius,
        coupler_gap=gap,
        coupling_angle_coverage=coupling_angle_coverage,
        length_x=length_x,
        cross_section_inner=cross_section_inner,
        cross_section_outer=cross_section_outer,
        bend=bend_all_angle,
        bend_output=bend_output,
    )
    cb = c << coupler

    cross_section = cross_section_inner
    straight = gf.c.straight
    sx = gf.get_component(
        straight, length=length_x, cross_section=cross_section, **kwargs
    )
    sy = gf.get_component(
        straight, length=length_y, cross_section=cross_section, **kwargs
    )
    b = gf.get_component(bend, cross_section=cross_section, radius=radius, **kwargs)
    sl = c << sy
    sr = c << sy
    bl = c << b
    br = c << b
    st = c << sx

    sl.connect(port="o1", other=cb["o2"])
    bl.connect(port="o2", other=sl["o2"], mirror=True)
    st.connect(port="o2", other=bl["o1"])
    sr.connect(port="o1", other=br["o1"])
    sr.connect(port="o2", other=cb["o3"])
    br.connect(port="o2", other=st["o1"], mirror=True)

    c.add_port("o2", port=cb["o4"])
    c.add_port("o1", port=cb["o1"])
    c.flatten()
    return c


if __name__ == "__main__":
    # c = coupler_bend()
    # n = c.get_netlist()
    c = ring_single_bend_coupler()
    # c = ring_single_bend_coupler()
    c.pprint_ports()
    c.plot()


import numpy as np
import gdsfactory as gf

@gf.cell
def gvc_bend(
    final_angle: float = 45.0,
    final_curvature: float = -0.1,
    peak_curvature: float = 0.2,
    cross_section: gf.typings.CrossSectionSpec = "strip",
    npoints: int = 720,
) -> gf.Component:
    """General Variable Curvature Bend.

    Args:
        final_angle: Final angle in degrees.
        final_curvature: Final curvature in 1/μm.
        peak_curvature: Peak curvature in 1/μm.
        cross_section: Cross-section spec.
        npoints: Number of points for discretization.

    """
    if peak_curvature <= 0 or final_curvature >= 0:
        raise ValueError("For this example, assume positive peak_curvature and negative final_curvature.")
    if peak_curvature + final_curvature <= 0:
        raise ValueError("peak_curvature must be > -final_curvature.")

    theta = np.deg2rad(final_angle)
    k_final = final_curvature
    k_peak = peak_curvature

    r = -k_final / k_peak + 1
    sp1 = 2 * theta / (k_peak + (k_peak + k_final) * r)
    sp2 = r * sp1

    # Discretize
    ds1 = sp1 / (npoints // 2 - 1) if sp1 > 0 else 0
    s1 = np.linspace(0, sp1, npoints // 2)
    k1 = (k_peak / sp1) * s1 if sp1 > 0 else np.array([])
    theta_cum1 = np.cumsum(k1) * ds1

    ds2 = sp2 / (npoints // 2 - 1) if sp2 > 0 else 0
    s2 = np.linspace(0, sp2, npoints // 2)
    k2 = k_peak + (k_final - k_peak) / sp2 * s2 if sp2 > 0 else np.array([])
    theta_cum2 = np.cumsum(k2) * ds2 + (theta_cum1[-1] if len(theta_cum1) > 0 else 0)

    theta_cum = np.concatenate((theta_cum1[:-1], theta_cum2)) if sp2 > 0 else theta_cum1

    ds = np.concatenate((np.full(len(theta_cum1) - 1, ds1), np.full(len(theta_cum2), ds2))) if sp2 > 0 else np.full(len(theta_cum), ds1)

    # Positions using trapezoid rule approximation, but for simplicity use cumsum * ds assuming dense points
    x = np.cumsum(np.cos(theta_cum) * ds)
    y = np.cumsum(np.sin(theta_cum) * ds)

    points = np.column_stack((x, y))

    p = gf.Path()
    p.points = points

    c = p.extrude(cross_section=cross_section)

    # Add ports
    #c.add_port(name="o1", center=(0, 0), orientation=0, cross_section=cross_section)

    final_center = points[-1]
    final_orientation = np.rad2deg(theta_cum[-1])
    #c.add_port(name="o2", center=final_center, orientation=final_orientation, cross_section=cross_section)

    return c

# Example usage
c = gvc_bend()
c.show()
c.plot()

import numpy as np
import gdsfactory as gf

@gf.cell
def gvc_bend(
    final_angle: float = 45.0,
    final_curvature: float = -0.1,
    peak_curvature: float = 0.2,
    cross_section: gf.typings.CrossSectionSpec = "strip",
    npoints: int = 720,
) -> gf.Component:
    """General Variable Curvature Bend.

    Args:
        final_angle: Final angle in degrees.
        final_curvature: Final curvature in 1/μm.
        peak_curvature: Peak curvature in 1/μm.
        cross_section: Cross-section spec.
        npoints: Number of points for discretization.

    """
    if peak_curvature <= 0 or final_curvature >= 0:
        raise ValueError("For this example, assume positive peak_curvature and negative final_curvature.")
    if peak_curvature + final_curvature <= 0:
        raise ValueError("peak_curvature must be > -final_curvature.")

    theta = np.deg2rad(final_angle)
    k_final = final_curvature
    k_peak = peak_curvature

    r = -k_final / k_peak + 1
    sp1 = 2 * theta / (k_peak + (k_peak + k_final) * r)
    sp2 = r * sp1

    # Discretize with trapezoid integration for accuracy
    n1 = npoints // 2
    n2 = npoints - n1  # Ensure total npoints

    s1 = np.linspace(0, sp1, n1)
    k1 = (k_peak / sp1) * s1 if sp1 > 0 else np.zeros(n1)
    theta_cum1 = np.cumtrapz(k1, s1, initial=0)  # Cumtrapz for precise cumulative integral

    s2 = np.linspace(0, sp2, n2)
    k2 = k_peak + (k_final - k_peak) / sp2 * s2 if sp2 > 0 else np.zeros(n2)
    theta_cum2 = np.cumtrapz(k2, s2, initial=0) + theta_cum1[-1]

    s = np.concatenate((s1[:-1], s1[-1] + s2)) if sp1 > 0 and sp2 > 0 else (s1 if sp1 > 0 else s2)
    theta_cum = np.concatenate((theta_cum1[:-1], theta_cum2)) if sp1 > 0 and sp2 > 0 else (theta_cum1 if sp1 > 0 else theta_cum2)

    # ds for position integration (approximate with diff(s))
    ds = np.diff(s, prepend=0)  # ds[0]=0, but positions start from 0

    # Positions: integrate cos/sin with trapezoid for better accuracy
    cos_theta = np.cos(theta_cum)
    sin_theta = np.sin(theta_cum)
    x = np.cumtrapz(cos_theta, s, initial=0)
    y = np.cumtrapz(sin_theta, s, initial=0)

    points = np.column_stack((x, y))

    p = gf.Path()
    p.points = points

    c = p.extrude(cross_section=cross_section)

    # Add ports
    c.add_port(name="o1", center=(0, 0), orientation=0, cross_section=cross_section)

    final_center = points[-1]
    final_orientation = np.rad2deg(theta_cum[-1])
    c.add_port(name="o2", center=final_center, orientation=final_orientation, cross_section=cross_section)

    return c

# Example usage
c = gvc_bend()
c.show()

import numpy as np
import gdsfactory as gf

@gf.cell
def gvc_bend_improved(
    final_angle: float = 45.0,
    final_curvature: float = -0.02,  # 1/μm，可以与angle符号不同
    transition_type: str = "smooth",  # "smooth", "linear", "s_curve"
    width: float = 0.5,
    layer: tuple = (1, 0),
    npoints: int = 401,
    auto_length: bool = True,
    target_length: float = None,
) -> gf.Component:
    """改进版通用可变曲率弯曲波导（GVC Bend）
    
    相比grok版本的改进：
    1. 端口正确对齐切线方向
    2. 统一的数值积分，避免分段误差
    3. 更灵活的参数组合（支持正角+负曲率）
    4. 自动长度计算或手动指定
    5. 多种过渡模式
    
    Args:
        final_angle: 最终角度（度），正=左转，负=右转
        final_curvature: 最终曲率（1/μm），可与angle符号独立
        transition_type: 曲率过渡类型
        width: 波导宽度
        layer: 绘制层
        npoints: 路径点数
        auto_length: 是否自动计算长度
        target_length: 手动指定长度（当auto_length=False时使用）
    """
    
    theta_target = np.deg2rad(final_angle)
    k_final = final_curvature
    
    # 参数验证（更宽松）
    if abs(theta_target) > np.pi:
        raise ValueError("final_angle should be within ±180 degrees")
    
    # 自动计算合适的长度或使用指定长度
    if auto_length:
        # 基于目标角度和曲率特性估算合理长度
        if abs(k_final) > 1e-6:
            # 有显著终曲率时，需要足够长度来过渡
            base_length = abs(theta_target / (k_final * 0.5)) if k_final != 0 else 50
            length = max(20, min(200, base_length))  # 限制在合理范围
        else:
            # 欧拉弯类型
            length = max(30, abs(theta_target) * 30)
    else:
        length = target_length if target_length is not None else 50
    
    # 参数化变量
    u = np.linspace(0, 1, npoints)
    
    # 根据过渡类型定义曲率分布 k(u)
    if transition_type == "linear":
        # 线性过渡：k(u) = k_final * u
        k_u = k_final * u
    elif transition_type == "smooth":
        # 平滑过渡：k(u) = k_final * (3u² - 2u³)  [S型]
        smooth_u = 3 * u**2 - 2 * u**3
        k_u = k_final * smooth_u
    elif transition_type == "s_curve":
        # S曲线：先增后减再增，适合复杂轨迹
        # k(u) = k_final * u + A * u * (1-u) * sin(π*u)
        A = k_final * 0.5  # 中间振荡幅度
        k_u = k_final * u + A * u * (1 - u) * np.sin(np.pi * u)
    else:
        raise ValueError(f"Unknown transition_type: {transition_type}")
    
    # 调整长度以匹配目标角度
    # ∫k(u)du * length = theta_target
    avg_curvature = np.trapz(k_u, u)  # ∫k(u)du from 0 to 1
    if abs(avg_curvature) > 1e-9:
        length_corrected = theta_target / avg_curvature
        if auto_length:
            length = abs(length_corrected)
        else:
            # 手动长度时，缩放曲率以匹配
            k_u = k_u * (theta_target / (avg_curvature * length))
    
    # 弧长参数
    s = u * length
    
    # 数值积分：计算角度 θ(s)
    theta = np.zeros(npoints)
    for i in range(1, npoints):
        # 梯形积分
        ds = s[i] - s[i-1]
        theta[i] = theta[i-1] + 0.5 * (k_u[i] + k_u[i-1]) * ds
    
    # 计算位置 x(s), y(s)
    cos_theta = np.cos(theta)
    sin_theta = np.sin(theta)
    
    x = np.zeros(npoints)
    y = np.zeros(npoints)
    for i in range(1, npoints):
        ds = s[i] - s[i-1]
        x[i] = x[i-1] + 0.5 * (cos_theta[i] + cos_theta[i-1]) * ds
        y[i] = y[i-1] + 0.5 * (sin_theta[i] + sin_theta[i-1]) * ds
    
    # 创建路径和组件
    points = list(zip(x, y))
    
    # 使用适当的平滑半径
    smooth_radius = max(0.1, length / npoints * 2)
    path = gf.path.smooth(points, radius=smooth_radius)
    
    cross_section = gf.cross_section.cross_section(width=width, layer=layer)
    
    c = gf.Component()
    waveguide_ref = c << path.extrude(cross_section)
    
    # 正确添加端口（沿切线方向）
    # 输入端口：起点，方向180度（向右传播）
    c.add_port(
        name="o1",
        center=(x[0], y[0]),
        width=width,
        orientation=180.0,  # 输入方向
        layer=layer
    )
    
    # 输出端口：终点，沿最终切线方向
    final_orientation_deg = float(np.rad2deg(theta[-1]))
    c.add_port(
        name="o2",
        center=(x[-1], y[-1]),
        width=width,
        orientation=final_orientation_deg,  # 沿切线，不是折线
        layer=layer
    )
    
    # 添加信息记录
    c.info.update({
        "final_angle_target": final_angle,
        "final_angle_actual": final_orientation_deg,
        "final_curvature_target": final_curvature,
        "final_curvature_actual": float(k_u[-1]),
        "transition_type": transition_type,
        "total_length": float(length),
        "auto_length": auto_length,
    })
    
    return c

# 对比测试：grok版本 vs 改进版本
print("=== 对比测试：正角度+负曲率 ===")

# grok版本（如果能运行的话）
try:
    c_grok = gvc_bend(final_angle=45.0, final_curvature=-0.02, peak_curvature=0.1)
    print("Grok版本端口信息:")
    c_grok.pprint_ports()
    print("实际终角:", np.rad2deg(np.arctan2(
        c_grok.ports["o2"].orientation_vector[1], 
        c_grok.ports["o2"].orientation_vector[0]
    )), "度")
except Exception as e:
    print(f"Grok版本运行错误: {e}")
    c_grok = None

print("\n改进版本端口信息:")
c_improved = gvc_bend_improved(
    final_angle=45.0, 
    final_curvature=-0.02,
    transition_type="smooth",
    auto_length=True
)
c_improved.pprint_ports()
print("组件信息:", c_improved.info)

# 显示结果
if c_grok:
    c_grok.plot()
c_improved.plot()
c_improved.show()

