import numpy as np
import gdsfactory as gf

@gf.cell
def gvc_bend(
    final_angle: float = 45.0,
    final_curvature: float = -0.1,
    peak_curvature: float = 0.2,
    cross_section: gf.typings.CrossSectionSpec = "strip",
    npoints: int = 720,
) -> gf.Component:
    """General Variable Curvature Bend.

    Args:
        final_angle: Final angle in degrees.
        final_curvature: Final curvature in 1/μm.
        peak_curvature: Peak curvature in 1/μm.
        cross_section: Cross-section spec.
        npoints: Number of points for discretization.

    """
    if peak_curvature <= 0 or final_curvature >= 0:
        raise ValueError("For this example, assume positive peak_curvature and negative final_curvature.")
    if peak_curvature + final_curvature <= 0:
        raise ValueError("peak_curvature must be > -final_curvature.")

    theta = np.deg2rad(final_angle)
    k_final = final_curvature
    k_peak = peak_curvature

    r = -k_final / k_peak + 1
    sp1 = 2 * theta / (k_peak + (k_peak + k_final) * r)
    sp2 = r * sp1

    # Discretize with trapezoid integration for accuracy
    n1 = npoints // 2
    n2 = npoints - n1  # Ensure total npoints

    s1 = np.linspace(0, sp1, n1)
    k1 = (k_peak / sp1) * s1 if sp1 > 0 else np.zeros(n1)
    theta_cum1 = np.cumtrapz(k1, s1, initial=0)  # Cumtrapz for precise cumulative integral

    s2 = np.linspace(0, sp2, n2)
    k2 = k_peak + (k_final - k_peak) / sp2 * s2 if sp2 > 0 else np.zeros(n2)
    theta_cum2 = np.cumtrapz(k2, s2, initial=0) + theta_cum1[-1]

    s = np.concatenate((s1[:-1], s1[-1] + s2)) if sp1 > 0 and sp2 > 0 else (s1 if sp1 > 0 else s2)
    theta_cum = np.concatenate((theta_cum1[:-1], theta_cum2)) if sp1 > 0 and sp2 > 0 else (theta_cum1 if sp1 > 0 else theta_cum2)

    # ds for position integration (approximate with diff(s))
    ds = np.diff(s, prepend=0)  # ds[0]=0, but positions start from 0

    # Positions: integrate cos/sin with trapezoid for better accuracy
    cos_theta = np.cos(theta_cum)
    sin_theta = np.sin(theta_cum)
    x = np.cumtrapz(cos_theta, s, initial=0)
    y = np.cumtrapz(sin_theta, s, initial=0)

    points = np.column_stack((x, y))

    p = gf.Path()
    p.points = points

    c = p.extrude(cross_section=cross_section)

    # Add ports
    c.add_port(name="o1", center=(0, 0), orientation=0, cross_section=cross_section)

    final_center = points[-1]
    final_orientation = np.rad2deg(theta_cum[-1])
    c.add_port(name="o2", center=final_center, orientation=final_orientation, cross_section=cross_section)

    return c

# Example usage
c = gvc_bend()
c.show()