{"cells": [{"cell_type": "code", "execution_count": 42, "id": "964ddd16", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> name </span>┃<span style=\"font-weight: bold\"> width </span>┃<span style=\"font-weight: bold\"> orientation </span>┃<span style=\"font-weight: bold\"> layer    </span>┃<span style=\"font-weight: bold\"> center                         </span>┃<span style=\"font-weight: bold\"> port_type </span>┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 2.0   │ 180.0       │ WG (1/0) │ (-30.809, -0.9460000000000001) │ optical   │\n", "│ o2   │ 1.5   │ 270.0       │ WG (1/0) │ (-50.0, -52.25)                │ optical   │\n", "│ o3   │ 1.5   │ 270.0       │ WG (1/0) │ (50.0, -52.25)                 │ optical   │\n", "│ o4   │ 2.0   │ 0.0         │ WG (1/0) │ (30.809, -0.9460000000000001)  │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴────────────────────────────────┴───────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mname\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mwidth\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1morientation\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mlayer   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mcenter                        \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mport_type\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 2.0   │ 180.0       │ WG (1/0) │ (-30.809, -0.9460000000000001) │ optical   │\n", "│ o2   │ 1.5   │ 270.0       │ WG (1/0) │ (-50.0, -52.25)                │ optical   │\n", "│ o3   │ 1.5   │ 270.0       │ WG (1/0) │ (50.0, -52.25)                 │ optical   │\n", "│ o4   │ 2.0   │ 0.0         │ WG (1/0) │ (30.809, -0.9460000000000001)  │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴────────────────────────────────┴───────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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************************************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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import gdsfactory as gf\n", "section_inner = gf.cross_section.cross_section(\n", "    width=1.5,\n", "    offset=0,\n", "    layer=(1, 0)\n", ")\n", "section_outer = gf.cross_section.cross_section(\n", "    width=2,\n", "    offset=0,\n", "    layer=(1, 0)\n", ")\n", "\n", "# 创建基础环形耦合器\n", "coupler = gf.components.coupler_ring_bend(\n", "    coupler_gap=0.5,\n", "    radius=50,\n", "    coupling_angle_coverage=20,\n", "    length_x=0,\n", "    cross_section_inner=section_inner,\n", "    cross_section_outer=section_outer,\n", "    bend_output=\"bend_euler\",\n", "    straight=\"straight\",\n", ")\n", "\n", "# 创建新的组件\n", "c = gf.Component()\n", "\n", "# 添加耦合器\n", "coupler_ref = c << coupler\n", "\n", "# 添加两个直波导\n", "s1 = c << gf.get_component(\"straight\", length=20, cross_section=section_outer)\n", "s2 = c << gf.get_component(\"straight\", length=20, cross_section=section_outer)\n", "\n", "# 连接直波导到耦合器的端口\n", "s1.connect(\"o1\", coupler_ref.ports[\"o1\"])\n", "s2.connect(\"o1\", coupler_ref.ports[\"o4\"])\n", "\n", "# 添加端口（新的端口位置）\n", "c.add_port(\"o1\", port=s1.ports[\"o2\"])\n", "c.add_port(\"o2\", port=coupler_ref.ports[\"o2\"])\n", "c.add_port(\"o3\", port=coupler_ref.ports[\"o3\"])\n", "c.add_port(\"o4\", port=s2.ports[\"o2\"])\n", "\n", "c.pprint_ports()\n", "c.draw_ports()\n", "c.plot()\n", "c.show()"]}, {"cell_type": "code", "execution_count": 54, "id": "a772169c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["端口信息:\n"]}, {"name": "stdout", "output_type": "stream", "text": ["端口信息:\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> name </span>┃<span style=\"font-weight: bold\"> width </span>┃<span style=\"font-weight: bold\"> orientation </span>┃<span style=\"font-weight: bold\"> layer    </span>┃<span style=\"font-weight: bold\"> center                        </span>┃<span style=\"font-weight: bold\"> port_type </span>┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 2.0   │ 180.0       │ WG (1/0) │ (-30.809, 24.679000000000002) │ optical   │\n", "│ o2   │ 1.5   │ 270.0       │ WG (1/0) │ (-50.0, -26.625)              │ optical   │\n", "│ o3   │ 1.5   │ 90.0        │ WG (1/0) │ (50.0, -26.625)               │ optical   │\n", "│ o4   │ 2.0   │ 0.0         │ WG (1/0) │ (30.809, 24.679000000000002)  │ optical   │\n", "│ o1   │ 2.0   │ 180.0       │ WG (1/0) │ (-30.809, 24.679000000000002) │ optical   │\n", "│ o2   │ 1.5   │ 270.0       │ WG (1/0) │ (-50.0, -26.625)              │ optical   │\n", "│ o3   │ 1.5   │ 90.0        │ WG (1/0) │ (50.0, -26.625)               │ optical   │\n", "│ o4   │ 2.0   │ 0.0         │ WG (1/0) │ (30.809, 24.679000000000002)  │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴───────────────────────────────┴───────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mname\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mwidth\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1morientation\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mlayer   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mcenter                       \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mport_type\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 2.0   │ 180.0       │ WG (1/0) │ (-30.809, 24.679000000000002) │ optical   │\n", "│ o2   │ 1.5   │ 270.0       │ WG (1/0) │ (-50.0, -26.625)              │ optical   │\n", "│ o3   │ 1.5   │ 90.0        │ WG (1/0) │ (50.0, -26.625)               │ optical   │\n", "│ o4   │ 2.0   │ 0.0         │ WG (1/0) │ (30.809, 24.679000000000002)  │ optical   │\n", "│ o1   │ 2.0   │ 180.0       │ WG (1/0) │ (-30.809, 24.679000000000002) │ optical   │\n", "│ o2   │ 1.5   │ 270.0       │ WG (1/0) │ (-50.0, -26.625)              │ optical   │\n", "│ o3   │ 1.5   │ 90.0        │ WG (1/0) │ (50.0, -26.625)               │ optical   │\n", "│ o4   │ 2.0   │ 0.0         │ WG (1/0) │ (30.809, 24.679000000000002)  │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴───────────────────────────────┴───────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["端口信息:\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> name </span>┃<span style=\"font-weight: bold\"> width </span>┃<span style=\"font-weight: bold\"> orientation </span>┃<span style=\"font-weight: bold\"> layer    </span>┃<span style=\"font-weight: bold\"> center                        </span>┃<span style=\"font-weight: bold\"> port_type </span>┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 2.0   │ 180.0       │ WG (1/0) │ (-30.809, 24.679000000000002) │ optical   │\n", "│ o2   │ 1.5   │ 270.0       │ WG (1/0) │ (-50.0, -26.625)              │ optical   │\n", "│ o3   │ 1.5   │ 90.0        │ WG (1/0) │ (50.0, -26.625)               │ optical   │\n", "│ o4   │ 2.0   │ 0.0         │ WG (1/0) │ (30.809, 24.679000000000002)  │ optical   │\n", "│ o1   │ 2.0   │ 180.0       │ WG (1/0) │ (-30.809, 24.679000000000002) │ optical   │\n", "│ o2   │ 1.5   │ 270.0       │ WG (1/0) │ (-50.0, -26.625)              │ optical   │\n", "│ o3   │ 1.5   │ 90.0        │ WG (1/0) │ (50.0, -26.625)               │ optical   │\n", "│ o4   │ 2.0   │ 0.0         │ WG (1/0) │ (30.809, 24.679000000000002)  │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴───────────────────────────────┴───────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mname\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mwidth\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1morientation\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mlayer   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mcenter                       \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mport_type\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 2.0   │ 180.0       │ WG (1/0) │ (-30.809, 24.679000000000002) │ optical   │\n", "│ o2   │ 1.5   │ 270.0       │ WG (1/0) │ (-50.0, -26.625)              │ optical   │\n", "│ o3   │ 1.5   │ 90.0        │ WG (1/0) │ (50.0, -26.625)               │ optical   │\n", "│ o4   │ 2.0   │ 0.0         │ WG (1/0) │ (30.809, 24.679000000000002)  │ optical   │\n", "│ o1   │ 2.0   │ 180.0       │ WG (1/0) │ (-30.809, 24.679000000000002) │ optical   │\n", "│ o2   │ 1.5   │ 270.0       │ WG (1/0) │ (-50.0, -26.625)              │ optical   │\n", "│ o3   │ 1.5   │ 90.0        │ WG (1/0) │ (50.0, -26.625)               │ optical   │\n", "│ o4   │ 2.0   │ 0.0         │ WG (1/0) │ (30.809, 24.679000000000002)  │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴───────────────────────────────┴───────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["c = gf.Component()\n", "    # 这里是波导（1，0）\n", "    #WG = gf.components.bends.bend_euler_s(layer=(1,0))\n", "    #WG=bend_s_offset(offset=offset, radius=radius)\n", "    #WG=gf.components.crossing_etched(width=0.8, r1=r1, r2=r2, w=w, L=3.5, layer_wg='WG')\n", "    #xs = gf.cross_section.strip(width=1, layer=(1, 0))\n", "    # WG=gf.components.straight(\n", "    # length=10,  # Length in microns\n", "    # cross_section=xs\n", "    # )       \n", "section_inner = gf.cross_section.cross_section(\n", "width=1.5,\n", "offset=0,\n", "layer=(1, 0)\n", ")\n", "section_outer = gf.cross_section.cross_section(\n", "    width=2,\n", "    offset=0,\n", "    layer=(1, 0)\n", ")\n", "\n", "# 创建基础环形耦合器\n", "coupler = gf.components.coupler_ring_bend(\n", "    coupler_gap=0.5,\n", "    radius=50,\n", "    coupling_angle_coverage=20,\n", "    length_x=0,\n", "    cross_section_inner=section_inner,\n", "    cross_section_outer=section_outer,\n", "    bend_output=\"bend_euler\",\n", "    straight=\"straight\",\n", ")\n", "\n", "# 创建新的组件\n", "WG = gf.Component()\n", "\n", "# 添加耦合器\n", "coupler_ref = WG << coupler\n", "\n", "# 添加两个直波导\n", "s1 = WG << gf.get_component(\"straight\", length=20, cross_section=section_outer)\n", "s2 = WG << gf.get_component(\"straight\", length=20, cross_section=section_outer)\n", "\n", "# 连接直波导到耦合器的端口\n", "s1.connect(\"o1\", coupler_ref.ports[\"o1\"])\n", "s2.connect(\"o1\", coupler_ref.ports[\"o4\"])\n", "\n", "# 添加端口（新的端口位置）\n", "WG.add_port(\"o1\", port=s1.ports[\"o2\"])\n", "# WG.add_port(\"o2\", port=coupler_ref.ports[\"o2\"])\n", "\n", "WG.add_port(\"o3\", port=coupler_ref.ports[\"o3\"])\n", "# WG.add_port(\"o4\", port=s2.ports[\"o2\"])\n", "# Add the MMI component as a reference to our main component `c`.\n", "WG_ref = c.add_ref(WG)\n", "# Create a rectangle background with the same size as the MMI.\n", "# The .size attribute is a convenient way to get the (width, height) tuple.\n", "box = gf.components.rectangle(\n", "    size=(WG_ref.xmax - WG_ref.xmin+3, WG_ref.ymax - WG_ref.ymin+6), layer=(3,0)\n", ")\n", "slab= gf.components.rectangle(\n", "    size=(WG_ref.xmax - WG_ref.xmin+3, WG_ref.ymax - WG_ref.ymin+6), layer=(2,0)\n", ")\n", "clad=gf.components.rectangle(\n", "    size=(WG_ref.xmax - WG_ref.xmin+3, WG_ref.ymax - WG_ref.ymin+6), layer=(5,0)\n", ")\n", "# Add the background rectangle as a reference to `c`.\n", "rect_ref = c.add_ref(box)\n", "slab_ref = c.add_ref(slab) \n", "clad_ref = c.add_ref(clad)\n", "# Align the center of the rectangle with the center of the MMI.\n", "WG_ref.center = (0, 0)\n", "rect_ref.center = WG_ref.center\n", "slab_ref.center = WG_ref.center\n", "clad_ref.center = WG_ref.center\n", "# Add the optical ports from the MMI reference to the main component `c`.\n", "c.add_ports(WG_ref.ports)\n", "c.plot()\n", "c.draw_ports()"]}, {"cell_type": "code", "execution_count": 34, "id": "e628e076", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import gdsfactory as gf\n", "# define a custom cross-section\n", "custom_in = gf.cross_section.cross_section(\n", "    width=1.5,\n", "    offset=0,\n", "    layer=(1, 0)\n", ")\n", "custom_out = gf.cross_section.cross_section(\n", "    width=2,\n", "    offset=0,\n", "    layer=(1, 0)\n", ")\n", "c = gf.components.ring_single_bend_coupler(\n", "    radius=50,\n", "    gap=2,\n", "    coupling_angle_coverage=120,\n", "    bend=\"bend_circular\",\n", "    bend_output=\"bend_euler\",\n", "    length_x=0,\n", "    length_y=0,\n", "    cross_section_inner=custom_in,\n", "    cross_section_outer=custom_out\n", ").copy()\n", "c.draw_ports()\n", "c.plot()"]}, {"cell_type": "code", "execution_count": 3, "id": "01334dae", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "module 'gdsfactory.component' has no attribute 'coupler_bend'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[3]\u001b[39m\u001b[32m, line 8\u001b[39m\n\u001b[32m      2\u001b[39m c = gf.Component()\n\u001b[32m      3\u001b[39m custom_in = gf.cross_section.cross_section(\n\u001b[32m      4\u001b[39m     width=\u001b[32m1.5\u001b[39m,\n\u001b[32m      5\u001b[39m     offset=\u001b[32m0\u001b[39m,\n\u001b[32m      6\u001b[39m     layer=(\u001b[32m1\u001b[39m, \u001b[32m0\u001b[39m)\n\u001b[32m      7\u001b[39m )\n\u001b[32m----> \u001b[39m\u001b[32m8\u001b[39m cp = \u001b[43mgf\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcomponent\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcoupler_bend\u001b[49m(\n\u001b[32m      9\u001b[39m         radius=\u001b[32m10\u001b[39m,\n\u001b[32m     10\u001b[39m         coupler_gap=\u001b[32m2\u001b[39m,\n\u001b[32m     11\u001b[39m         coupling_angle_coverage=\u001b[32m180\u001b[39m,\n\u001b[32m     12\u001b[39m         cross_section_inner=custom_in,\n\u001b[32m     13\u001b[39m         cross_section_outer=custom_in,\n\u001b[32m     14\u001b[39m \n\u001b[32m     15\u001b[39m     )\n\u001b[32m     16\u001b[39m cp.show()\n\u001b[32m     17\u001b[39m sin = gf.get_component(straight, length=length_x, cross_section=cross_section_inner)\n", "\u001b[31mAttributeError\u001b[39m: module 'gdsfactory.component' has no attribute 'coupler_bend'"]}], "source": ["import gdsfactory as gf\n", "c = gf.Component()\n", "custom_in = gf.cross_section.cross_section(\n", "    width=1.5,\n", "    offset=0,\n", "    layer=(1, 0)\n", ")\n", "cp = gf.component.coupler_bend(\n", "        radius=10,\n", "        coupler_gap=2,\n", "        coupling_angle_coverage=180,\n", "        cross_section_inner=custom_in,\n", "        cross_section_outer=custom_in,\n", "       \n", "    )\n", "cp.show()\n", "sin = gf.get_component(straight, length=length_x, cross_section=cross_section_inner)\n", "sout = gf.get_component(\n", "        straight, length=length_x, cross_section=cross_section_outer\n", "    )\n", "\n", "coupler_right = c << cp\n", "coupler_left = c << cp\n", "straight_inner = c << sin\n", "straight_inner.movex(-length_x / 2)\n", "straight_outer = c << sout\n", "straight_outer.movex(-length_x / 2)\n", "\n", "coupler_left.connect(\"o1\", straight_outer.ports[\"o1\"])\n", "straight_inner.connect(\"o1\", coupler_left.ports[\"o2\"])\n", "coupler_right.connect(\"o2\", straight_inner.ports[\"o2\"], mirror=True)\n", "straight_outer.connect(\"o2\", coupler_right.ports[\"o1\"])\n", "\n", "c.add_port(\"o1\", port=coupler_left.ports[\"o3\"])\n", "c.add_port(\"o2\", port=coupler_left.ports[\"o4\"])\n", "c.add_port(\"o4\", port=coupler_right.ports[\"o3\"])\n", "c.add_port(\"o3\", port=coupler_right.ports[\"o4\"])\n", "    # c.flatten()\n", "  "]}, {"cell_type": "code", "execution_count": null, "id": "27ba2c38", "metadata": {}, "outputs": [], "source": ["@gf.cell_with_module_name\n", "def coupler_ring_bend(\n", "    radius: float | None = None,\n", "    coupler_gap: float = 0.2,\n", "    coupling_angle_coverage: float = 90.0,\n", "    length_x: float = 0.0,\n", "    cross_section_inner: CrossSectionSpec = \"strip\",\n", "    cross_section_outer: CrossSectionSpec = \"strip\",\n", "    bend: AnyComponentFactory = bend_circular_all_angle,\n", "    bend_output: ComponentSpec = \"bend_euler\",\n", "    straight: ComponentSpec = \"straight\",\n", ") -> Component:\n", "    r\"\"\"Two back-to-back coupler_bend.\n", "\n", "    Args:\n", "        radius: um. Default is None, which uses the default radius of the cross_section.\n", "        coupler_gap: um.\n", "        angle_inner: of the inner bend, from beginning to end. Depending on the bend chosen, gap may not be preserved.\n", "        angle_outer: of the outer bend, from beginning to end. Depending on the bend chosen, gap may not be preserved.\n", "        coupling_angle_coverage: degrees.\n", "        length_x: horizontal straight length.\n", "        cross_section_inner: spec inner bend.\n", "        cross_section_outer: spec outer bend.\n", "        bend: for bend.\n", "        bend_output: for bend.\n", "        straight: for straight.\n", "    \"\"\"\n", "    c = Component()\n", "    cp = coupler_bend(\n", "        radius=radius,\n", "        coupler_gap=coupler_gap,\n", "        coupling_angle_coverage=coupling_angle_coverage,\n", "        cross_section_inner=cross_section_inner,\n", "        cross_section_outer=cross_section_outer,\n", "        bend=bend,\n", "        bend_output=bend_output,\n", "    )\n", "    sin = gf.get_component(straight, length=length_x, cross_section=cross_section_inner)\n", "    sout = gf.get_component(\n", "        straight, length=length_x, cross_section=cross_section_outer\n", "    )\n", "\n", "    coupler_right = c << cp\n", "    coupler_left = c << cp\n", "    straight_inner = c << sin\n", "    straight_inner.movex(-length_x / 2)\n", "    straight_outer = c << sout\n", "    straight_outer.movex(-length_x / 2)\n", "\n", "    coupler_left.connect(\"o1\", straight_outer.ports[\"o1\"])\n", "    straight_inner.connect(\"o1\", coupler_left.ports[\"o2\"])\n", "    coupler_right.connect(\"o2\", straight_inner.ports[\"o2\"], mirror=True)\n", "    straight_outer.connect(\"o2\", coupler_right.ports[\"o1\"])\n", "\n", "    c.add_port(\"o1\", port=coupler_left.ports[\"o3\"])\n", "    c.add_port(\"o2\", port=coupler_left.ports[\"o4\"])\n", "    c.add_port(\"o4\", port=coupler_right.ports[\"o3\"])\n", "    c.add_port(\"o3\", port=coupler_right.ports[\"o4\"])\n", "    # c.flatten()\n", "    return c"]}, {"cell_type": "code", "execution_count": 9, "id": "f77b67d2", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import gdsfactory as gf\n", "\n", "c = gf.components.coupler_bent(gap=0.2, radius=26, length=5, width1=0.4, width2=0.4, length_straight=0, cross_section='strip').copy()\n", "c.draw_ports()\n", "c.plot()"]}, {"cell_type": "code", "execution_count": 15, "id": "9df2d650", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\python\\python3_12\\Lib\\site-packages\\gdsfactory\\path.py:1443: RuntimeWarning: divide by zero encountered in scalar divide\n", "  npoints = max(int(npoints), int(360 / angle) + 1)\n"]}, {"ename": "OverflowError", "evalue": "cannot convert float infinity to integer", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mOverflowError\u001b[39m                             Traceback (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[15]\u001b[39m\u001b[32m, line 137\u001b[39m\n\u001b[32m    133\u001b[39m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m c\n\u001b[32m    136\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[34m__name__\u001b[39m == \u001b[33m\"\u001b[39m\u001b[33m__main__\u001b[39m\u001b[33m\"\u001b[39m:\n\u001b[32m--> \u001b[39m\u001b[32m137\u001b[39m     c = \u001b[43mcoupler_bent_half\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    138\u001b[39m     \u001b[38;5;66;03m# c = coupler_bent_half()\u001b[39;00m\n\u001b[32m    139\u001b[39m     \u001b[38;5;66;03m# c = coupler_bent()\u001b[39;00m\n\u001b[32m    140\u001b[39m     c.plot()\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\python\\python3_12\\Lib\\site-packages\\kfactory\\layout.py:1003\u001b[39m, in \u001b[36mKCLayout.cell.<locals>.decorator_autocell.<locals>.func\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m   1001\u001b[39m \u001b[38;5;129m@functools\u001b[39m.wraps(f)\n\u001b[32m   1002\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mfunc\u001b[39m(*args: KCellParams.args, **kwargs: KCellParams.kwargs) -> KC:\n\u001b[32m-> \u001b[39m\u001b[32m1003\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mwrapper_autocell\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\python\\python3_12\\Lib\\site-packages\\kfactory\\decorators.py:458\u001b[39m, in \u001b[36mWrappedKCellFunc.__call__\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m    457\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m__call__\u001b[39m(\u001b[38;5;28mself\u001b[39m, *args: KCellParams.args, **kwargs: KCellParams.kwargs) -> KC:\n\u001b[32m--> \u001b[39m\u001b[32m458\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_f\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\python\\python3_12\\Lib\\site-packages\\kfactory\\decorators.py:432\u001b[39m, in \u001b[36mWrappedKCellFunc.__init__.<locals>.wrapper_autocell\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m    429\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m output_type(base=cell.base)\n\u001b[32m    431\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m kcl.thread_lock:\n\u001b[32m--> \u001b[39m\u001b[32m432\u001b[39m     cell_ = \u001b[43mwrapped_cell\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    433\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m cell_.destroyed():\n\u001b[32m    434\u001b[39m         \u001b[38;5;66;03m# If any cell has been destroyed, we should clean up the cache.\u001b[39;00m\n\u001b[32m    435\u001b[39m         \u001b[38;5;66;03m# Delete all the KCell entrances in the cache which have\u001b[39;00m\n\u001b[32m    436\u001b[39m         \u001b[38;5;66;03m# `destroyed() == True`\u001b[39;00m\n\u001b[32m    437\u001b[39m         deleted_cell_hashes: \u001b[38;5;28mlist\u001b[39m[\u001b[38;5;28mint\u001b[39m] = [\n\u001b[32m    438\u001b[39m             _hash_item\n\u001b[32m    439\u001b[39m             \u001b[38;5;28;01mfor\u001b[39;00m _hash_item, _cell_item \u001b[38;5;129;01min\u001b[39;00m cache.items()\n\u001b[32m    440\u001b[39m             \u001b[38;5;28;01mif\u001b[39;00m _cell_item.destroyed()\n\u001b[32m    441\u001b[39m         ]\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\python\\python3_12\\Lib\\site-packages\\cachetools\\_cached.py:173\u001b[39m, in \u001b[36m_locked.<locals>.wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m    171\u001b[39m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m:\n\u001b[32m    172\u001b[39m         \u001b[38;5;28;01mpass\u001b[39;00m  \u001b[38;5;66;03m# key not found\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m173\u001b[39m v = \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    174\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m lock:\n\u001b[32m    175\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    176\u001b[39m         \u001b[38;5;66;03m# in case of a race, prefer the item already in the cache\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\python\\python3_12\\Lib\\site-packages\\kfactory\\decorators.py:383\u001b[39m, in \u001b[36mWrappedKCellFunc.__init__.<locals>.wrapper_autocell.<locals>.wrapped_cell\u001b[39m\u001b[34m(**params)\u001b[39m\n\u001b[32m    381\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    382\u001b[39m     name_ = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m383\u001b[39m cell = \u001b[43mf\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m  \u001b[38;5;66;03m# type: ignore[call-arg]\u001b[39;00m\n\u001b[32m    384\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m cell \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    385\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[32m    386\u001b[39m         \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mThe cell function \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m.name\u001b[38;5;132;01m!r}\u001b[39;00m\u001b[33m in \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mstr\u001b[39m(\u001b[38;5;28mself\u001b[39m.file)\u001b[38;5;132;01m!r}\u001b[39;00m\u001b[33m\"\u001b[39m\n\u001b[32m    387\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33m returned None. Did you forget to return the cell or component\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    388\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33m at the end of the function?\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    389\u001b[39m     )\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[15]\u001b[39m\u001b[32m, line 40\u001b[39m, in \u001b[36mcoupler_bent_half\u001b[39m\u001b[34m(gap, radius, length, width1, width2, length_straight, length_straight_exit, cross_section)\u001b[39m\n\u001b[32m     37\u001b[39m xs1 = xs.copy(radius=radius_outer, width=width1)\n\u001b[32m     38\u001b[39m xs2 = xs.copy(radius=radius_inner, width=width2)\n\u001b[32m---> \u001b[39m\u001b[32m40\u001b[39m outer_bend = \u001b[43mgf\u001b[49m\u001b[43m.\u001b[49m\u001b[43mpath\u001b[49m\u001b[43m.\u001b[49m\u001b[43marc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mangle\u001b[49m\u001b[43m=\u001b[49m\u001b[43m-\u001b[49m\u001b[43malpha\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mradius\u001b[49m\u001b[43m=\u001b[49m\u001b[43mradius_outer\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     41\u001b[39m inner_bend = gf.path.arc(angle=-alpha, radius=radius_inner)\n\u001b[32m     43\u001b[39m outer_straight = gf.path.straight(length=length, npoints=\u001b[32m100\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\python\\python3_12\\Lib\\site-packages\\gdsfactory\\path.py:1443\u001b[39m, in \u001b[36marc\u001b[39m\u001b[34m(radius, angle, npoints, start_angle)\u001b[39m\n\u001b[32m   1440\u001b[39m     \u001b[38;5;28;01<PERSON>raise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33marc() requires a radius argument\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m   1442\u001b[39m npoints = npoints \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mabs\u001b[39m(\u001b[38;5;28mint\u001b[39m(angle / \u001b[32m360\u001b[39m * radius / PDK.bend_points_distance / \u001b[32m2\u001b[39m))\n\u001b[32m-> \u001b[39m\u001b[32m1443\u001b[39m npoints = \u001b[38;5;28mmax\u001b[39m(\u001b[38;5;28mint\u001b[39m(npoints), \u001b[38;5;28;43mint\u001b[39;49m\u001b[43m(\u001b[49m\u001b[32;43m360\u001b[39;49m\u001b[43m \u001b[49m\u001b[43m/\u001b[49m\u001b[43m \u001b[49m\u001b[43mangle\u001b[49m\u001b[43m)\u001b[49m + \u001b[32m1\u001b[39m)\n\u001b[32m   1445\u001b[39m t = np.linspace(\n\u001b[32m   1446\u001b[39m     start_angle * np.pi / \u001b[32m180\u001b[39m, (angle + start_angle) * np.pi / \u001b[32m180\u001b[39m, npoints\n\u001b[32m   1447\u001b[39m )\n\u001b[32m   1448\u001b[39m x = radius * np.cos(t)\n", "\u001b[31mOverflowError\u001b[39m: cannot convert float infinity to integer"]}], "source": ["import numpy as np\n", "\n", "import gdsfactory as gf\n", "\n", "\n", "@gf.cell_with_module_name\n", "def coupler_bent_half(\n", "    gap: float = 0.200,\n", "    radius: float = 26,\n", "    length: float = 0,\n", "    width1: float = 0.400,\n", "    width2: float = 0.400,\n", "    length_straight: float = 0,\n", "    length_straight_exit: float = 0,\n", "    cross_section: str = \"strip\",\n", ") -> gf.Component:\n", "    \"\"\"Returns Broadband SOI curved / straight directional coupler.\n", "\n", "    Args:\n", "        gap: gap.\n", "        radius: radius coupling.\n", "        length: coupler_length.\n", "        width1: width1.\n", "        width2: width2.\n", "        length_straight: input and output straight length.\n", "        length_straight_exit: length straight exit.\n", "        cross_section: cross_section.\n", "    \"\"\"\n", "    radius_outer = radius + (width1 + gap) / 2\n", "    radius_inner = radius - (width2 + gap) / 2\n", "    alpha = round(np.rad2deg(length / (2 * radius)), 4)\n", "    beta = alpha\n", "\n", "    c = gf.Component()\n", "\n", "    xs = gf.get_cross_section(cross_section)\n", "    xs1 = xs.copy(radius=radius_outer, width=width1)\n", "    xs2 = xs.copy(radius=radius_inner, width=width2)\n", "\n", "    outer_bend = gf.path.arc(angle=-alpha, radius=radius_outer)\n", "    inner_bend = gf.path.arc(angle=-alpha, radius=radius_inner)\n", "\n", "    outer_straight = gf.path.straight(length=length, npoints=100)\n", "    inner_straight = gf.path.straight(length=length, npoints=100)\n", "\n", "    outer_exit_bend = gf.path.arc(angle=alpha, radius=radius_outer)\n", "    inner_exit_bend_down = gf.path.arc(angle=-beta, radius=radius_inner)\n", "    inner_exit_bend_up = gf.path.arc(angle=alpha + beta, radius=radius_inner)\n", "\n", "    inner_exit_straight = gf.path.straight(\n", "        length=length_straight,\n", "        npoints=100,\n", "    )\n", "    outer_exit_straight = gf.path.straight(\n", "        length=length_straight_exit,\n", "        npoints=100,\n", "    )\n", "\n", "    outer = outer_bend + outer_straight + outer_exit_bend + outer_exit_straight\n", "    inner = (\n", "        inner_bend\n", "        + inner_straight\n", "        + inner_exit_bend_down\n", "        + inner_exit_bend_up\n", "        + inner_exit_straight\n", "    )\n", "\n", "    inner_component = c << inner.extrude(xs2)\n", "    outer_component = c << outer.extrude(xs1)\n", "    outer_component.movey(+(width1 + gap) / 2)\n", "    inner_component.movey(-(width2 + gap) / 2)\n", "\n", "    c.add_port(\"o1\", port=outer_component.ports[\"o1\"])\n", "    c.add_port(\"o2\", port=inner_component.ports[\"o1\"])\n", "    c.add_port(\"o3\", port=outer_component.ports[\"o2\"])\n", "    c.add_port(\"o4\", port=inner_component.ports[\"o2\"])\n", "    c.flatten()\n", "    return c\n", "\n", "\n", "@gf.cell_with_module_name\n", "def coupler_bent(\n", "    gap: float = 0.200,\n", "    radius: float = 26,\n", "    length: float = 8.6,\n", "    width1: float = 0.400,\n", "    width2: float = 0.400,\n", "    length_straight: float = 10,\n", "    cross_section: str = \"strip\",\n", ") -> gf.Component:\n", "    \"\"\"Returns Broadband SOI curved / straight directional coupler.\n", "\n", "    based on: https://doi.org/10.1038/s41598-017-07618-6.\n", "\n", "    Args:\n", "        gap: gap.\n", "        radius: radius coupling.\n", "        length: coupler_length.\n", "        width1: width1.\n", "        width2: width2.\n", "        length_straight: input and output straight length.\n", "        cross_section: cross_section.\n", "    \"\"\"\n", "    c = gf.Component()\n", "\n", "    right_half = c << coupler_bent_half(\n", "        gap=gap,\n", "        radius=radius,\n", "        length=length,\n", "        width1=width1,\n", "        width2=width2,\n", "        length_straight=length_straight,\n", "        cross_section=cross_section,\n", "    )\n", "    left_half = c << coupler_bent_half(\n", "        gap=gap,\n", "        radius=radius,\n", "        length=length,\n", "        width1=width1,\n", "        width2=width2,\n", "        length_straight=length_straight,\n", "        cross_section=cross_section,\n", "    )\n", "\n", "    left_half.connect(port=\"o1\", other=right_half.ports[\"o1\"], mirror=True)\n", "\n", "    c.add_port(\"o1\", port=left_half.ports[\"o3\"])\n", "    c.add_port(\"o2\", port=left_half.ports[\"o4\"])\n", "    c.add_port(\"o3\", port=right_half.ports[\"o3\"])\n", "    c.add_port(\"o4\", port=right_half.ports[\"o4\"])\n", "\n", "    c.flatten()\n", "    return c\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    c = coupler_bent_half()\n", "    # c = coupler_bent_half()\n", "    # c = coupler_bent()\n", "    c.plot()\n"]}, {"cell_type": "code", "execution_count": 17, "id": "0f8d6b7d", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAzAAAAJoCAYAAAC5ogQ1AAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAMTgAADE4Bf3eMIwAAHjVJREFUeJzt3Q1y48iVhdGko3daG+FGtNacgN0cs2X+SgQy78tzIr546uqasQBCEF5RZZ96770BAAAE+NfoTwAAAOBVFhgAACCGBQYAAIhhgQEAAGJYYAAAgBgWGAAAIIYFBgAAiPHXs99wOp2O+UwAAIDl9Sf/M5XegQEAAGJYYAAAgBgWGAAAIIYFBgAAiGGBAQAAYlhgAACAGBYYAAAghgUGAACIYYEBAABiWGAAAIAYFhgAACCGBQYAAIhhgQEAAGJYYAAAgBgWGAAAIIYFBgAAiGGBAQAAYlhgAACAGBYYAAAghgUGAACIYYEBAABiTL/AnM/nVk21Y6p2PBWPqdrxVDymasdT8ZiqHc/GMc2v2vFUPKZqx5NwTKfee3/4G06n4z4bAABgaf3xejL/OzAAAAAXFhgAACCGBQYAAIhhgQEAAGJYYAAAgBgWGAAAIIYFBgAAiGGBAQAAYlhgAACAGBYYAAAghgUGAACIYYEBAABiWGAAAIAYFhgAACCGBQYAAIhhgQEAAGJYYAAAgBgWGAAAIIYFBgAAiGGBAQAAYlhgAACAGBYYAAAghgUGAACIYYEBAABiWGAAAIAYFhgAACCGBQYAAIhhgQEAAGJYYAAAgBgWGAAAIIYFBgAAiGGBAQAAYlhgAACAGBYYAAAghgUGAACIYYEBAABiWGAAAIAYFhgAACCGBQYAAIhhgQEAAGJYYAAAgBgWGAAAIIYFBgAAiGGBAQAAYlhgAACAGBYYAAAghgUGAACIYYEBAABiWGAAAIAYFhgAACDG9AvM+Xxu1VQ7pmrHU/GYqh1PxWOqdjwVj6na8Wwc0/yqHU/FY6p2PAnHdOq994e/4XQ67rMBAACW1h+vJ/O/AwMAAHBhgQEAAGJYYAAAgBgWGAAAIIYFBgAAiGGBAQAAYlhgAACAGBYYAAAghgUGAACIYYEBAABiWGAAAIAYFhgAACCGBQYAAIhhgQEAAGJYYAAAgBgWGAAAIIYFBgAAiGGBAQAAYlhgAACAGBYYAAAghgUGAACIYYEBAABiWGAAAIAYFhgAACCGBQYAAIhhgQEAAGJYYAAAgBgWGAAAIIYFBgAAiGGBAQAAYlhgAACAGBYYAAAghgUGAACIYYEBAABiWGAAAIAYFhgAACCGBQYAAIhhgQEAAGJYYAAAgBgWGAAAIIYFBgAAiGGBAQAAYlhgAACAGBYYAAAghgUGAACIYYEBAABiWGAAAIAYFhgAACCGBQYAAIhhgQEAAGJYYAAAgBjTLzDn87lVU+2Yqh1PxWOqdjwVj6na8VQ8pmrHs3FM86t2PBWPqdrxJBzTqffeH/6G0+m4zwYAAFhaf7yezP8ODAAAwIUFBgAAiGGBAQAAYlhgAACAGBYYAAAghgUGAACIYYEBAABiWGAAAIAYFhgAACCGBQYAAIhhgQEAAGJYYAAAgBgWGAAAIMZfR/6H9a+vw/6zTn/+HPafBQAAM+oFn78PfwfmcmB7TwAAoNV7/u5PbL/lU/Wvr393+XiveflYkiRJWrke+Pz9dD85eoE5ckqSJEkr1wOfv6dbYNI2QEmSJCm1Hvj8Pd0Cc+SUJEmSVq4HPn9Pt8CkbYCSJElSaj3w+Xu6BebIKUmSJK1cD3z+nm6BSdsAJUmSpNR64PP3dAvMkVOSJElauR74/D3dApO2AUqSJEmp9cDn7+kWmCOnJEmStHI98Pl7ugUmbQOUJEmSUuuBz9/TLTBHTkmSJGnleuDz93QLTNoGKEmSJKXWA5+/p1tgjpySJEnSyvXA5+/pFpi0DVCSJElKrQc+f0+3wBw5JUmSpJXrgc/f0y0waRugJEmSlFoPfP6eboE5ckqSJEkr1wOfv6dbYNI2QEmSJCm1Hvj8Pd0Cc+SUJEmSVq4HPn9Pt8CkbYCSJElSaj3w+Xu6BebIKUmSJK1aP2h5uZ4lF5i0DVCSJElKrB+4vJReYI6ckiRJ0qp178DknEQLjCRJklavewcm6ySOvmAkSZKkkXXvwOScRAuMJEmSVq97BybrJI6+YCRJkqSRde/ArHsSJUmSpLS6d2DWPYmSJElSWj30zYPpFpjEkyhJkiSl1UPfPJhugUk8iZIkSVJaPfTNg+kWmMSTKEmSJKXVQ988mG6BSTyJkiRJUlo99M2D6RaYxJMoSZIkpdVD3zyYboF5d57P5+En8dNtxzT6c3A8ax1TteOpeEzVjqfiMVU7HseUUbXjqXhMMx9P/+GbBz99/i67wHgHRpIkSdq/HvrcPd0Cc9S0wEiSJGnl+kFvHpRfYFI3QUmSJCmpHvrcPd0Cc9S0wEiSJGnlundgck7i9ZQkSZJWrIc+d0+3wBw1LTCSJElaue4dmJyTeD0lSZKkFeuhz93TLTBHTQuMJEmSVq57B+ZzJ/LIKUmSJK1YD33unnKBSdwEJUmSpKR66HP3lAvMkVOSJElasR763D3lApO4CUqSJElJ9dDn7ikXmCOnJEmStGI99Ll7ygUmcROUJEmSkuqhz91TLjBHTkmSJGm1evD//uKUC0ziJihJkiSl1A9cXpZYYI6ckiRJ0mp178DknUwLjCRJklatewcm82SOvnAkSZKkEXXvwOSdTAuMJEmSVq17BybzZI6+cCRJkqQRde/AOJmSJElSSt07ME6mJEmSlFIPftNgugXmyJNogZEkSdKK9eDn7WkXGO/ASJIkSfvUg5+3p11gjpySJEnSSvXg5+1pF5jUjVCSJEmavR78vD3tAnPklCRJklaqBz9vT7vApG6EkiRJ0sz1A5eXpRaY5JMqSZIkzVoP/q9QnnqBST6pkiRJ0qz18DcLpl1gkk+qJEmSNGs9/K9rTLvAeAdGkiRJ+nw9/Dl72gXmqGmBkSRJ0kp178DkntTrKUmSJK1QD3/OnnaBOWpaYCRJkrRS3TswuSf1ekqSJEkr1MOfs6ddYI6aFhhJkiStUi/wnD3lAnP0yR19IUmSJElH1Av8pNPUC0zyZihJkiTNVvcOjHdgJEmSpJS6d2C8AyNJkiSl1L0DU+MdGEuMJEmSVqh7Byb/HZg9T64kSZI0U73AmwRTLzDpJ1eSJEmaqV7gTYJpF5gjT6oFRpIkSSvUCzxfT7/AeAdGkiRJ+ky9wPP19AvMkVOSJEmqXC/wfD39ApO+IUqSJEmz1As8X0+/wBw1LTGSJEmqXC/wF/gjFpgKJ1mSJEkaXS/y5sD0C0yFkyxJkiSNrhd5c2D6BabCSX638/k8/HNwPGsdU7XjqXhM1Y6n4jFVOx7HlFG146l4TDMdTy/y5sDUC8z3E5B6kiVJkqTR9SLP1RELzJFTkiRJqlgv8lwdscBU2BQlSZKkkfUiz9URC8xR0xIjSZKkivVCf7c8YoGpcrIlSZKkEfVCz9MRC8xR0wIjSZKkivUiPz4Ws8BU2hglSZKkI+vFnqenX2AqnnRJkiTpqHqxn2iKWWAqnXRJkiTpqHqxNwNiFphKJ12SJEk6ql7szYCYBabSSZckSZKOqhf6C/wxC8zeJ/vWlCRJktLrBX+SKWqBqXbyJUmSpD3rBX+SKWqBqXbyJUmSpD3rBd8EiFpgqp18SZIkac96wTcBohaYaidfkiRJqvAmQD/w+Tlmgdn7pN+akiRJUmq96E8wxS0wFV8ESZIk6dP1oj/BFLfAVHwRJEmSpE/Xi/7hf9wCU/FFkCRJkj5dL/qH/3ELTMUXQZIkSfpk/eA/9LfAPHghLDGSJEnSus/NUQvMiBdj9MUnSZIkvVsvurzELjBVXwxJkiTpE/XCf+gfucBUfTEkSZKk39aLPy/HLTArvCiSJEnST+vFf2IpdoGp/KJIkiRJP60X/8P+2AWm8osiSZIk/bRe/A/7YxeYyi+KJEmS9JP6wX/Ib4F544Wp/KJIkiRJCc/J3QIz74sz+mKUJEmSntWLLy/xC0z1F0eSJEl6tRV+fKylLzCWGEmSJGmtn1CKXWBWepEkSZKkZ63yh/vxC8wKL5IkSZL0qFV+fKxVWGBWeJEkSZKkR6301yviF5gRU5IkSZqplZ6LoxeYo18kC4wkSZJma6XlpVVZYCwxkiRJWrXVnodLLDAjpiRJkjRDKy0vJRaYFV80SZIkadU/zC+zwFhiJEmStForPgeXWWBGTEmSJGlkKz4Hl1lgVts8JUmStHYrLi+tygJzOaGWGEmSJK3Sqs+/pRaYEVOSJEka0arPv2UWmJVfREmSJK3Vys+95RaYFd9GkyRJ0lqt/NxbboEZMSVJkqQjW3V5adUWmKNfzOspSZIkHdHqz7slF5iVN1JJkiTVbvXn3fgF5nw+/88LevT89Iv6/ZjSq3Y8FY+p2vFUPKZqx1PxmKodj2PKqNrxVDymTx/P0ctLu/EOzOjXKH6BuffCjnxRJUmSpD3ynNvqLjBeXEmSJFWqwk8atU+ch4oLzPcTvuqLK0mSpDp5vm31F5gRU5IkSdojz7et/gLjRZYkSVKFPNe2+gvM5cR7m02SJEnJ+bsvba0FxostSZKk5PxkUVtngRnxIs/8YkuSJCkry0tbc4HxokuSJCkxP1HU1lxgvOiSJElKy3NsW3OBGfXijz5mSZIkZecnidraC4wXX5IkSSmNen7tAc+wSywwIy+C0cctSZKkvCwv7f65WWmBGTklSZKkV/Lc2h6fn5UWGJusJEmSZs9za3t8flZZYFwMkiRJmj3Pq+35OVppgRl5UYw+bkmSJM2f5aU9P0crLjAjpyRJknQrz6nttfO02gIz6qJIuzAkSZJ0XJaX9vq5WnWBscRIkiRpljyfttfP1YoLjItEkiRJs+S5tL13vlZeYEZOSZIkactzaXvvfK28wLhYJEmSNLKRz6M99Jl02QXm+wvnYpEkSdLReR5t75+zlRcYF40kSZJG5Tm0/ey8WWC8bSdJkqRj89cZ2s/P3eoLzMiLJv3ikSRJ0vtZXtrvzp8FxkUkSZKk4/ITQO13588C898LyRIjSZKkPbO8tN+fQwuMi0mSJEm1/9C8F3retMB8u6BGzUoXlSRJkv43P/HTPnMeLTD/e2GNmpUuLEmSJP03y0v73Lm0wMx1cVW7wCRJklbP82X77Pm0wMx3kY0+fkmSJNV4ruwFny0tMHcutBmmJEmSsrO8tM+fUwvMfBfb9ZQkSVJmo58je9HnSQuMi06SJEkfbvTzYy/8HGmBeXLhedtPkiRJ7zR6aenFnyMtMC9cgC4+SZIkvZrnx7bv+bXAPL8AR8/qF6EkSVKVLC9t/3NsgXntQhw9V7gYJUmSkhu9vFzPVjgLzBsX5AxTkiRJ82V5acedawvMexfmDFOSJEnzZHlpx55vC4yLU5IkST9v9HPhan/d4BkLzI0LdIaLdPR5kCRJ0vjnwdWWl2aB+fmFOsPFOvo8SJIkrdzo58AVl5dmgcm9WK+nJEmSjm3089/Kz4PPWGBctJIkSbpq9HPf6s+Bz1hgXLySJEn6u9HPe57/mgWmyt+FWfkiliRJOqLRz3me+9p/jt8C8/sLefTF62KWJEnat9HPd9dz9We+ZywwL17QM1zM11OSJEmfafRzneWl1VpgzufzHCdykiVmxgt7ltfIMa1zPBWPqdrxVDymasfjmDKqdjwzHtMMz3eXOcsz3nnwaxS/wMzULEvM9ZQkSdLPGv0cN+Py0ibIAvPJkznBxX1rSpIk6b1GP79ZXtr918YC8+ETOsFFfmtKkiTptUY/t3mea49fHwvMDid1kovcRS9JkvReo5/XPMe156+RBWanEzvJxe7ilyRJeq3Rz2me39prr5MFZp2/0O+LQJIk6Xajn89uTc9u7fZrZYHZ8eRaYiRJkqZv9HOZ57X23utlgdn5BFtiJEmSpmzW5zTPau3x62aBWfeLY/R5kSRJGtWsz2ee0drz184Cc9CJ9kUiSZI0RZ7LWnQWmCNPti8WSZKkoXkea/FZYI4+4b5oJEmShuQ5rJXIAjPipPvikSRJOqzLc84Mz1vfp+ev9v7raYEZdOJ9EUmSJO3eDM9X96bnrvaz19QCM/DkT7zE+IKSJEnpzfBcdW961mo/f10tMINfgEmXmOspSZKU1OzPV56x2u9eXwvM4Bdggi+iZ9MXmSRJSsny0spngZmg7z+2NeP0xSZJkmbP89QaWWAmaYYvqmfTF54kSZoxfxi8Vs9YYI58MQK++K6nJEnS6EY/D1le2vGvuQVmviwxkiRJNZ6XPDO1z7/2Fpg580UpSZJ0O89Ja/eMBWbkixP0xekLVJIk7d33546Zp2ejtt91YIGZu6QvUl+okiRpr2Z43nl1eiZq+14LFpj5S1livn+ukiRJv81zkNr3a8ICk5EvXkmStFLff8IjYXr+acdcGxaYnBK/iH0hS5Kkd5vhOebd6ZmnHXd9WGCySltivn/OkiRJ9/Kco/bKdWKBySv5i9sXuCRJ+t7354Sk6dmmHX+9WGAyS/0i//65S5KkdUt/nhl9/lbtGQvM5I3+ovWOjCRJWukdl+upNub6scDkN/qL1zsykiTplSosLZ5Z2vAsMEUa/UXsHRlJklT5HRfPKW2aLDCFqnBzuD6O0edTkiT9Ls8lantcVxaYelW6WbhhSJKUlz9UVdvz+rLA1KvSTeP6eEafV0mS9DjPH2pHXGcWmLpVvIm4mUiSNFe3vkdXmJ432rRZYBZohpvAp6cbiyRJY6u4tFxPtWmzwCzS6JuAZUaSpPyqLy2eJ1pEFpiFGn0zsMxIkpTXCkuL54cWlQVmsVa4CV0f5+jzLUlSYp4X1CbOArNoK92U3JwkSXreSkvL9fGOPu9qb2eBWbwZbh5HTQuNJEn/bMWl5XqqRWaB0dI3L8uMJGm1fN/3fb+FZ4HRfy+GRW9m18fuxiZJqtat73ErTt/fW5ksMPrnBbH4ze36HFhoJEmJ3fs+tvL0vbyVygKj2xfGBDeb2aaFRpI0Y5aVx9+3R78+ah/PAqP7F4eb4d15q9GvlySpfo++B5n//D49+rVS2y0LjJ5fJG6OlhpJ0uFZViwvajezwOj1i2WCm1LatNhIkp717PuF+fr329GvpdohWWD03gXjpvqxabmRpHWypOz7/XT066t2aPELzPl8Hv45rHhMq990t9doxHKz10064Zpb/ZiqHU/FY6p2PI7p9Z7dt5O/H80+E5YXX0ft48UvMBrbETdn8/Xl5ohlR5JW6N17rXn898HR14jasCww+khu4nPOTzb6GpOkV/rkPc+cb/p+pPbCAnP6e0m563Q6PfrXLKZ/ff3/x6c/f/79z2aNCZBi9P3S3Of7z/bPsHmynjxfcUZvYJozf4JlmqZpmuZv5+VjqV3lR8i0a5ebzww3QdM0TdM055+Xtn+W2o0sMDqk65uRaZqmaZrmrXn5WGoPssDo0C43pxlukqZpmqZpjp+Xtn+W2gtZYDSk6xvWDDdP0zRN0zSPm9dtvya1N7LAaHjXNzDTNE3TNGvO67Zfk9oPs8Bomr7f2EzTNE3TrDEvbf8stV9mgdF0Xd/oZrjpmqZpmqb5/ry0/bPUPpgFRlN3fQOc4WZsmqZpmub9ed32a1LbIQuMYrq+IZqmaZqmOce8bvs1qe2cBUZxfb9RmqZpmqY5Zl4+ltqBWWAU3eXmOcNN3DRN0zQrz+9tvy61AVlgVKZ7N1fTNE3TNH82r9t+TWoTZIFR2W7deE3TNE3TfDyv235NapNlgdES3bohm6Zpmqb5n3nd9mtSmzgLjJbs+43aNE3TNFeZ39t+XWpBWWC0fPdu5qZpmqZZZV63/ZrUgrPASDf6frOf4ZuPaZqmab4yv7f9utQKZYGRXujWN4QZvkmZpmmaa89bbf9OaoWzwEg/7NE3DtM0TdP89LzV9u+k1XrGAiO90aNvMKZpmqb56rzV9u8kNQuMdET3vhnN8E3SNE3THDPvtf17Se1uFhhpcI++iV1/MzNN0zRz5rO23yep/SgLjBTQK98Mr78pmqZpmp+fr7b9fklttywwUqHe+QZ775uuaZpm5fmbtv8fktrwnjn9vaTcdTqdHv1rIET/+hr9KQDs7vTnz+hPAfilJ+tJs8AAAAAxC8y/DvtMAAAAfskCAwAAxLDAAAAAMSwwAABADAsMAAAQwwIDAADEsMAAAAAxLDAAAEAMCwwAABDjr9GfAPn619foTwEACHL682f0p0Aw78Dw8ZuRaZqmaZrmvQm/1p/Yfov0qP71ZZqmaZqm+daU2p2e7icWGP227UY0+iZomqZpmmbGvHwstTtZYLR7M9wMTdM0TdPMmlK7kwVGu7fdiEbfBE3TNE3TzJiXj6V2JwuMdm+Gm6FpmqZpmllTaneywGj3thvR6JugaZqmaZoZ8/Kx1O5kgdHuzXAzNE3TNE0za0rtThYY7d52Ixp9EzRN0zRNM2NePpbanSww2r0ZboamaZqmaWZNqd3JAqPd225Eo2+CpmmapmlmzMvHUruTBUa7N8PN0DRN0zTNrCm1qgvM+Xwe/jk4psdtN6LRN0Fzvbl9Hc3weTgeM3m67swR8/Jxlao917UJjil+gdH8zXAzNE3TNE0za0rtThYY7d52Ixp9EzRN0zRNM2NePpbanSww2r0ZboamaZqmaWZNqd3JAqPd225Eo2+CpmmapmlmzMvHUruTBUa7N8PN0DRN0zTNrCm1O1lgtHvbjWj0TdA0TdM0zYx5+Vhqd7LAaPdmuBmapmmappk1pXYnC4x2b7sRjb4JmqZpmqaZMS8fS+1OFhjt3gw3Q9M0TdM0s6bU7mSB0e5tN6LRN0HTNE3TNDPm5WOp3ckCo92b4WZomqZpmmbWlNqdLDDave1GNPomaJqmaZpmxrx8LLU7PXP6e0m563Q6PfrX0PrX1+hPAQAIcvrzZ/SnwMSerCfNAgMAAMQsMP867DMBAAD4JQsMAAAQwwIDAADEsMAAAAAxLDAAAEAMCwwAABDDAgMAAMSwwAAAADEsMAAAQAwLDAAAEMMCAwAAxLDAAAAAMSwwAABADAsMAAAQwwIDAADEsMAAAAAxLDAAAEAMCwwAABDDAgMAAMSwwAAAADEsMAAAQAwLDAAAEMMCAwAAxLDAAAAAMSwwAABADAsMAAAQwwIDAADEsMAAAAAxLDAAAEAMCwwAABDDAgMAAMSwwAAAADEsMAAAQAwLDAAAEMMCAwAAxLDAAAAAMSwwAABADAsMAAAQY/oF5nw+t2qqHVO146l4TNWOp+IxVTueisdU7Xg2jml+1Y6n4jFVO56EYzr13vvD33A6HffZAAAAS+uP15P534EBAAC4sMAAAAAxLDAAAEAMCwwAABDDAgMAAMSwwAAAADEsMAAAQAwLDAAAEMMCAwAAxLDAAAAAMSwwAABADAsMAAAQwwIDAADEsMAAAAAxLDAAAEAMCwwAABDDAgMAAMSwwAAAADEsMAAAQAwLDAAAEMMCAwAAxLDAAAAAMSwwAABADAsMAAAQwwIDAADEsMAAAAAxLDAAAEAMCwwAABDDAgMAAMSwwAAAADEsMAAAQAwLDAAAEMMCAwAAxLDAAAAAMSwwAABADAsMAAAQwwIDAADEsMAAAAAxLDAAAEAMCwwAABDDAgMAAMSwwAAAADEsMAAAQAwLDAAAEMMCAwAAxLDAAAAAMSwwAABADAsMAAAQwwIDAADEsMAAAAAxpl9gzudzq6baMVU7norHVO14Kh5TteOpeEzVjmfjmOZX7XgqHlO140k4plPvvT/8DafTcZ8NAACwtP54PZn/HRgAAIALCwwAABDDAgMAAMSwwAAAADEsMAAAQAwLDAAAEMMCAwAAxLDAAAAAMSwwAABADAsMAAAQwwIDAADEsMAAAAAxLDAAAECMv0Z/Atx3Pp8f/jMAAKzGAjOpbVm5tcBYYgAAWJkfIZvUbxeVW//31792WYYuv2Y5AgAggXdgFnZrebHIAAAwM+/AhLBYAACAd2Cmd/3OCAAArM4CMzHvugAAwD/5EbJFlheLEAAAFXgHZmLP/pvE3vm/924OAAAVnHrv/eFvOJ2O+2z4GAsLAACJnqwnfoQMAADIYYEBAABi+BEyAABgGn6EDAAAKMMCAwAAxDj8v0bZfzNWBq8TAAAz8r8D8yGVHvgrHQsAALX4ETIAACCGBQYAAIhhgQEAAGJYYAAAgBj+Ev+H+IvvAACwv1N/8j91eTqdDvg0AAAAWnuynvgRMgAAIIcFBgAAiGGBAQAAYlhgAACAGBYYAAAghgUGAACIYYEBAABiWGAAAIAYFhgAACCGBQYAAIhhgQEAAGJYYAAAgBgWGAAAIIYFBgAAiGGBAQAAYvz17Df03o/5TAAAAJ7wDgwAABDDAgMAAMSwwAAAADEsMAAAQAwLDAAAEMMCAwAAxLDAAAAALcX/AU+GzXU02OR+AAAAAElFTkSuQmCC", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from __future__ import annotations\n", "\n", "import gdsfactory as gf\n", "from gdsfactory.component import Component\n", "from gdsfactory.components.couplers.coupler import coupler_straight\n", "from gdsfactory.components.couplers.coupler90 import coupler90\n", "from gdsfactory.typings import ComponentSpec, CrossSectionSpec\n", "\n", "\n", "@gf.cell_with_module_name\n", "def coupler_ring(\n", "    gap: float = 0.2,\n", "    radius: float = 5.0,\n", "    length_x: float = 4.0,\n", "    bend: ComponentSpec = \"bend_euler\",\n", "    straight: ComponentSpec = \"straight\",\n", "    cross_section: CrossSectionSpec = \"strip\",\n", "    cross_section_bend: CrossSectionSpec | None = None,\n", "    length_extension: float = 3.0,\n", ") -> Component:\n", "    r\"\"\"<PERSON><PERSON><PERSON> for ring.\n", "\n", "    Args:\n", "        gap: spacing between parallel coupled straight waveguides.\n", "        radius: of the bends.\n", "        length_x: length of the parallel coupled straight waveguides.\n", "        bend: 90 degrees bend spec.\n", "        straight: straight spec.\n", "        cross_section: cross_section spec.\n", "        cross_section_bend: optional bend cross_section spec.\n", "        length_extension: straight length extension at the end of the coupler bottom ports.\n", "\n", "    .. code::\n", "\n", "          o2            o3\n", "           |             |\n", "            \\           /\n", "             \\         /\n", "           ---=========---\n", "        o1    length_x   o4\n", "\n", "          o2                              o3\n", "          xx                              xx\n", "          xx                             xx\n", "           xx          length_x          x\n", "            xx     ◄───────────────►    x\n", "             xx                       xxx\n", "               xx                   xxx\n", "                xxx──────▲─────────xxx\n", "                         │gap\n", "                 o1──────▼─────────◄──────────────► o4\n", "                                    length_extension\n", "    \"\"\"\n", "    if length_extension is None:\n", "        length_extension = 3 + radius\n", "\n", "    c = Component()\n", "    gap = gf.snap.snap_to_grid(gap, grid_factor=2)\n", "    cross_section_bend = cross_section_bend or cross_section\n", "\n", "    # define subcells\n", "    coupler90_component = gf.get_component(\n", "        coupler90,\n", "        gap=gap,\n", "        radius=radius,\n", "        bend=bend,\n", "        straight=straight,\n", "        cross_section=cross_section,\n", "        cross_section_bend=cross_section_bend,\n", "        length_straight=length_extension,\n", "    )\n", "    coupler_straight_component = gf.get_component(\n", "        coupler_straight,\n", "        gap=gap,\n", "        length=length_x,\n", "        cross_section=cross_section,\n", "    )\n", "\n", "    # add references to subcells\n", "    cbl = c << coupler90_component\n", "    cbr = c << coupler90_component\n", "    cs = c << coupler_straight_component\n", "\n", "    # connect references\n", "    cs.connect(port=\"o4\", other=cbr.ports[\"o1\"])\n", "    cbl.connect(port=\"o2\", other=cs.ports[\"o2\"], mirror=True)\n", "\n", "    c.add_port(\"o1\", port=cbl.ports[\"o4\"])\n", "    c.add_port(\"o2\", port=cbl.ports[\"o3\"])\n", "    c.add_port(\"o3\", port=cbr.ports[\"o3\"])\n", "    c.add_port(\"o4\", port=cbr.ports[\"o4\"])\n", "\n", "    c.add_ports(\n", "        gf.port.select_ports_list(ports=cbl.ports, port_type=\"electrical\"), prefix=\"cbl\"\n", "    )\n", "    c.add_ports(\n", "        gf.port.select_ports_list(ports=cbr.ports, port_type=\"electrical\"), prefix=\"cbr\"\n", "    )\n", "    c.auto_rename_ports()\n", "    c.flatten()\n", "    return c\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    c = coupler_ring()\n", "    c.plot()\n"]}, {"cell_type": "code", "execution_count": 22, "id": "0d34b2ac", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> name </span>┃<span style=\"font-weight: bold\"> width </span>┃<span style=\"font-weight: bold\"> orientation </span>┃<span style=\"font-weight: bold\"> layer    </span>┃<span style=\"font-weight: bold\"> center                        </span>┃<span style=\"font-weight: bold\"> port_type </span>┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o2   │ 0.5   │ 0.0         │ WG (1/0) │ (11.402000000000001, -4.598)  │ optical   │\n", "│ o1   │ 0.5   │ 180.0       │ WG (1/0) │ (-11.402000000000001, -4.598) │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴───────────────────────────────┴───────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mname\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mwidth\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1morientation\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mlayer   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mcenter                       \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mport_type\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o2   │ 0.5   │ 0.0         │ WG (1/0) │ (11.402000000000001, -4.598)  │ optical   │\n", "│ o1   │ 0.5   │ 180.0       │ WG (1/0) │ (-11.402000000000001, -4.598) │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴───────────────────────────────┴───────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from __future__ import annotations\n", "\n", "from typing import Any\n", "\n", "import gdsfactory as gf\n", "from gdsfactory.component import Component\n", "from gdsfactory.components.bends.bend_circular import bend_circular_all_angle\n", "from gdsfactory.typings import AnyComponentFactory, ComponentSpec, CrossSectionSpec\n", "\n", "\n", "@gf.cell_with_module_name\n", "def coupler_bend(\n", "    radius: float = 10.0,\n", "    coupler_gap: float = 0.2,\n", "    coupling_angle_coverage: float = 120.0,\n", "    cross_section_inner: CrossSectionSpec = \"strip\",\n", "    cross_section_outer: CrossSectionSpec = \"strip\",\n", "    bend: AnyComponentFactory = bend_circular_all_angle,\n", "    bend_output: ComponentSpec = \"bend_euler\",\n", ") -> Component:\n", "    r\"\"\"Compact curved coupler with bezier escape.\n", "\n", "    TODO: fix for euler bends.\n", "\n", "    Args:\n", "        radius: um.\n", "        coupler_gap: um.\n", "        coupling_angle_coverage: degrees.\n", "        cross_section_inner: spec inner bend.\n", "        cross_section_outer: spec outer bend.\n", "        bend: for bend.\n", "        bend_output: for bend.\n", "\n", "    .. code::\n", "\n", "            r   4\n", "            |   |\n", "            |  / ___3\n", "            | / /\n", "        2____/ /\n", "        1_____/\n", "    \"\"\"\n", "    c = Component()\n", "\n", "    xi = gf.get_cross_section(cross_section_inner)\n", "    xo = gf.get_cross_section(cross_section_outer)\n", "\n", "    angle_inner = 90\n", "    angle_outer = coupling_angle_coverage / 2\n", "    gap = coupler_gap\n", "\n", "    width = xo.width / 2 + xi.width / 2\n", "    spacing = gap + width\n", "\n", "    bend90_inner_right = gf.get_component(\n", "        bend,  # type: ignore[arg-type]\n", "        radius=radius,\n", "        cross_section=cross_section_inner,\n", "        angle=angle_inner,\n", "    )\n", "    bend_output_right = gf.get_component(\n", "        bend,  # type: ignore[arg-type]\n", "        radius=radius + spacing,\n", "        cross_section=cross_section_outer,\n", "        angle=angle_outer,\n", "    )\n", "    bend_inner_ref = c.create_vinst(bend90_inner_right)\n", "    bend_output_ref = c.create_vinst(bend_output_right)\n", "\n", "    output = gf.get_component(\n", "        bend_output, angle=angle_outer, cross_section=cross_section_outer\n", "    )\n", "    output_ref = c.create_vinst(output)\n", "    output_ref.connect(\"o1\", bend_output_ref.ports[\"o2\"], mirror=True)\n", "\n", "    pbw = bend_inner_ref.ports[\"o1\"]\n", "    bend_inner_ref.movey(pbw.center[1] + spacing)\n", "\n", "    c.add_port(\"o1\", port=bend_output_ref.ports[\"o1\"])\n", "    c.add_port(\"o2\", port=bend_inner_ref.ports[\"o1\"])\n", "    c.add_port(\"o3\", port=output_ref.ports[\"o2\"])\n", "    c.add_port(\"o4\", port=bend_inner_ref.ports[\"o2\"])\n", "    return c\n", "\n", "\n", "@gf.cell_with_module_name\n", "def coupler_ring_bend(\n", "    radius: float = 10.0,\n", "    coupler_gap: float = 0.2,\n", "    coupling_angle_coverage: float = 90.0,\n", "    length_x: float = 0.0,\n", "    cross_section_inner: CrossSectionSpec = \"strip\",\n", "    cross_section_outer: CrossSectionSpec = \"strip\",\n", "    bend: AnyComponentFactory = bend_circular_all_angle,\n", "    bend_output: ComponentSpec = \"bend_euler\",\n", "    straight: ComponentSpec = \"straight\",\n", ") -> Component:\n", "    r\"\"\"Two back-to-back coupler_bend.\n", "\n", "    Args:\n", "        radius: um.\n", "        coupler_gap: um.\n", "        angle_inner: of the inner bend, from beginning to end. Depending on the bend chosen, gap may not be preserved.\n", "        angle_outer: of the outer bend, from beginning to end. Depending on the bend chosen, gap may not be preserved.\n", "        coupling_angle_coverage: degrees.\n", "        length_x: horizontal straight length.\n", "        cross_section_inner: spec inner bend.\n", "        cross_section_outer: spec outer bend.\n", "        bend: for bend.\n", "        bend_output: for bend.\n", "        straight: for straight.\n", "    \"\"\"\n", "    c = Component()\n", "    cp = coupler_bend(\n", "        radius=radius,\n", "        coupler_gap=coupler_gap,\n", "        coupling_angle_coverage=coupling_angle_coverage,\n", "        cross_section_inner=cross_section_inner,\n", "        cross_section_outer=cross_section_outer,\n", "        bend=bend,\n", "        bend_output=bend_output,\n", "    )\n", "    sin = gf.get_component(straight, length=length_x, cross_section=cross_section_inner)\n", "    sout = gf.get_component(\n", "        straight, length=length_x, cross_section=cross_section_outer\n", "    )\n", "\n", "    coupler_right = c << cp\n", "    coupler_left = c << cp\n", "    straight_inner = c << sin\n", "    straight_inner.movex(-length_x / 2)\n", "    straight_outer = c << sout\n", "    straight_outer.movex(-length_x / 2)\n", "\n", "    coupler_left.connect(\"o1\", straight_outer.ports[\"o1\"])\n", "    straight_inner.connect(\"o1\", coupler_left.ports[\"o2\"])\n", "    coupler_right.connect(\"o2\", straight_inner.ports[\"o2\"], mirror=True)\n", "    straight_outer.connect(\"o2\", coupler_right.ports[\"o1\"])\n", "\n", "    c.add_port(\"o1\", port=coupler_left.ports[\"o3\"])\n", "    c.add_port(\"o2\", port=coupler_left.ports[\"o4\"])\n", "    c.add_port(\"o4\", port=coupler_right.ports[\"o3\"])\n", "    c.add_port(\"o3\", port=coupler_right.ports[\"o4\"])\n", "    # c.flatten()\n", "    return c\n", "\n", "\n", "@gf.cell_with_module_name\n", "def ring_single_bend_coupler(\n", "    radius: float = 5.0,\n", "    gap: float = 0.2,\n", "    coupling_angle_coverage: float = 90.0,\n", "    bend_all_angle: AnyComponentFactory = bend_circular_all_angle,\n", "    bend: ComponentSpec = \"bend_circular\",\n", "    bend_output: ComponentSpec = \"bend_euler\",\n", "    length_x: float = 0.6,\n", "    length_y: float = 0.6,\n", "    cross_section_inner: CrossSectionSpec = \"strip\",\n", "    cross_section_outer: CrossSectionSpec = \"strip\",\n", "    **kwargs: Any,\n", ") -> Component:\n", "    r\"\"\"Returns ring with curved coupler.\n", "\n", "    TODO: enable euler bends.\n", "\n", "    Args:\n", "        radius: um.\n", "        gap: um.\n", "        coupling_angle_coverage: degrees.\n", "        angle_inner: of the inner bend, from beginning to end. Depending on the bend chosen, gap may not be preserved.\n", "        angle_outer: of the outer bend, from beginning to end. Depending on the bend chosen, gap may not be preserved.\n", "        bend_all_angle: for bend.\n", "        bend: for bend.\n", "        bend_output: for bend.\n", "        length_x: horizontal straight length.\n", "        length_y: vertical straight length.\n", "        cross_section_inner: spec inner bend.\n", "        cross_section_outer: spec outer bend.\n", "        kwargs: cross_section settings.\n", "    \"\"\"\n", "    c = Component()\n", "\n", "    coupler = coupler_ring_bend(\n", "        radius=radius,\n", "        coupler_gap=gap,\n", "        coupling_angle_coverage=coupling_angle_coverage,\n", "        length_x=length_x,\n", "        cross_section_inner=cross_section_inner,\n", "        cross_section_outer=cross_section_outer,\n", "        bend=bend_all_angle,\n", "        bend_output=bend_output,\n", "    )\n", "    cb = c << coupler\n", "\n", "    cross_section = cross_section_inner\n", "    straight = gf.c.straight\n", "    sx = gf.get_component(\n", "        straight, length=length_x, cross_section=cross_section, **kwargs\n", "    )\n", "    sy = gf.get_component(\n", "        straight, length=length_y, cross_section=cross_section, **kwargs\n", "    )\n", "    b = gf.get_component(bend, cross_section=cross_section, radius=radius, **kwargs)\n", "    sl = c << sy\n", "    sr = c << sy\n", "    bl = c << b\n", "    br = c << b\n", "    st = c << sx\n", "\n", "    sl.connect(port=\"o1\", other=cb[\"o2\"])\n", "    bl.connect(port=\"o2\", other=sl[\"o2\"], mirror=True)\n", "    st.connect(port=\"o2\", other=bl[\"o1\"])\n", "    sr.connect(port=\"o1\", other=br[\"o1\"])\n", "    sr.connect(port=\"o2\", other=cb[\"o3\"])\n", "    br.connect(port=\"o2\", other=st[\"o1\"], mirror=True)\n", "\n", "    c.add_port(\"o2\", port=cb[\"o4\"])\n", "    c.add_port(\"o1\", port=cb[\"o1\"])\n", "    c.flatten()\n", "    return c\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    # c = coupler_bend()\n", "    # n = c.get_netlist()\n", "    c = ring_single_bend_coupler()\n", "    # c = ring_single_bend_coupler()\n", "    c.pprint_ports()\n", "    c.plot()\n"]}, {"cell_type": "code", "execution_count": 9, "id": "1efe5363", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import gdsfactory as gf\n", "\n", "@gf.cell\n", "def gvc_bend(\n", "    final_angle: float = 45.0,\n", "    final_curvature: float = -0.1,\n", "    peak_curvature: float = 0.2,\n", "    cross_section: gf.typings.CrossSectionSpec = \"strip\",\n", "    npoints: int = 720,\n", ") -> gf.Component:\n", "    \"\"\"General Variable Curvature Bend.\n", "\n", "    Args:\n", "        final_angle: Final angle in degrees.\n", "        final_curvature: Final curvature in 1/μm.\n", "        peak_curvature: Peak curvature in 1/μm.\n", "        cross_section: Cross-section spec.\n", "        npoints: Number of points for discretization.\n", "\n", "    \"\"\"\n", "    if peak_curvature <= 0 or final_curvature >= 0:\n", "        raise ValueError(\"For this example, assume positive peak_curvature and negative final_curvature.\")\n", "    if peak_curvature + final_curvature <= 0:\n", "        raise ValueError(\"peak_curvature must be > -final_curvature.\")\n", "\n", "    theta = np.deg2rad(final_angle)\n", "    k_final = final_curvature\n", "    k_peak = peak_curvature\n", "\n", "    r = -k_final / k_peak + 1\n", "    sp1 = 2 * theta / (k_peak + (k_peak + k_final) * r)\n", "    sp2 = r * sp1\n", "\n", "    # Discretize\n", "    ds1 = sp1 / (npoints // 2 - 1) if sp1 > 0 else 0\n", "    s1 = np.linspace(0, sp1, npoints // 2)\n", "    k1 = (k_peak / sp1) * s1 if sp1 > 0 else np.array([])\n", "    theta_cum1 = np.cumsum(k1) * ds1\n", "\n", "    ds2 = sp2 / (npoints // 2 - 1) if sp2 > 0 else 0\n", "    s2 = np.linspace(0, sp2, npoints // 2)\n", "    k2 = k_peak + (k_final - k_peak) / sp2 * s2 if sp2 > 0 else np.array([])\n", "    theta_cum2 = np.cumsum(k2) * ds2 + (theta_cum1[-1] if len(theta_cum1) > 0 else 0)\n", "\n", "    theta_cum = np.concatenate((theta_cum1[:-1], theta_cum2)) if sp2 > 0 else theta_cum1\n", "\n", "    ds = np.concatenate((np.full(len(theta_cum1) - 1, ds1), np.full(len(theta_cum2), ds2))) if sp2 > 0 else np.full(len(theta_cum), ds1)\n", "\n", "    # Positions using trapezoid rule approximation, but for simplicity use cumsum * ds assuming dense points\n", "    x = np.cumsum(np.cos(theta_cum) * ds)\n", "    y = np.cumsum(np.sin(theta_cum) * ds)\n", "\n", "    points = np.column_stack((x, y))\n", "\n", "    p = gf.Path()\n", "    p.points = points\n", "\n", "    c = p.extrude(cross_section=cross_section)\n", "\n", "    # Add ports\n", "    #c.add_port(name=\"o1\", center=(0, 0), orientation=0, cross_section=cross_section)\n", "\n", "    final_center = points[-1]\n", "    final_orientation = np.rad2deg(theta_cum[-1])\n", "    #c.add_port(name=\"o2\", center=final_center, orientation=final_orientation, cross_section=cross_section)\n", "\n", "    return c\n", "\n", "# Example usage\n", "c = gvc_bend()\n", "c.show()\n", "c.plot()"]}, {"cell_type": "code", "execution_count": 17, "id": "b1c91c5b", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import gdsfactory as gf\n", "\n", "@gf.cell\n", "def gvc_bend(\n", "    final_angle: float = 45.0,\n", "    final_curvature: float = -0.1,\n", "    peak_curvature: float = 0.2,\n", "    cross_section: gf.typings.CrossSectionSpec = \"strip\",\n", "    npoints: int = 720,\n", ") -> gf.Component:\n", "    \"\"\"General Variable Curvature Bend.\n", "\n", "    Args:\n", "        final_angle: Final angle in degrees.\n", "        final_curvature: Final curvature in 1/μm.\n", "        peak_curvature: Peak curvature in 1/μm.\n", "        cross_section: Cross-section spec.\n", "        npoints: Number of points for discretization.\n", "\n", "    \"\"\"\n", "    if peak_curvature <= 0 or final_curvature >= 0:\n", "        raise ValueError(\"For this example, assume positive peak_curvature and negative final_curvature.\")\n", "    if peak_curvature + final_curvature <= 0:\n", "        raise ValueError(\"peak_curvature must be > -final_curvature.\")\n", "\n", "    theta = np.deg2rad(final_angle)\n", "    k_final = final_curvature\n", "    k_peak = peak_curvature\n", "\n", "    r = -k_final / k_peak + 1\n", "    sp1 = 2 * theta / (k_peak + (k_peak + k_final) * r)\n", "    sp2 = r * sp1\n", "\n", "    # Discretize with trapezoid integration for accuracy\n", "    n1 = npoints // 2\n", "    n2 = npoints - n1  # Ensure total npoints\n", "\n", "    s1 = np.linspace(0, sp1, n1)\n", "    k1 = (k_peak / sp1) * s1 if sp1 > 0 else np.zeros(n1)\n", "    theta_cum1 = np.cumtrapz(k1, s1, initial=0)  # Cumtrapz for precise cumulative integral\n", "\n", "    s2 = np.linspace(0, sp2, n2)\n", "    k2 = k_peak + (k_final - k_peak) / sp2 * s2 if sp2 > 0 else np.zeros(n2)\n", "    theta_cum2 = np.cumtrapz(k2, s2, initial=0) + theta_cum1[-1]\n", "\n", "    s = np.concatenate((s1[:-1], s1[-1] + s2)) if sp1 > 0 and sp2 > 0 else (s1 if sp1 > 0 else s2)\n", "    theta_cum = np.concatenate((theta_cum1[:-1], theta_cum2)) if sp1 > 0 and sp2 > 0 else (theta_cum1 if sp1 > 0 else theta_cum2)\n", "\n", "    # ds for position integration (approximate with diff(s))\n", "    ds = np.diff(s, prepend=0)  # ds[0]=0, but positions start from 0\n", "\n", "    # Positions: integrate cos/sin with trapezoid for better accuracy\n", "    cos_theta = np.cos(theta_cum)\n", "    sin_theta = np.sin(theta_cum)\n", "    x = np.cumtrapz(cos_theta, s, initial=0)\n", "    y = np.cumtrapz(sin_theta, s, initial=0)\n", "\n", "    points = np.column_stack((x, y))\n", "\n", "    p = gf.Path()\n", "    p.points = points\n", "\n", "    c = p.extrude(cross_section=cross_section)\n", "\n", "    # Add ports\n", "    c.add_port(name=\"o1\", center=(0, 0), orientation=0, cross_section=cross_section)\n", "\n", "    final_center = points[-1]\n", "    final_orientation = np.rad2deg(theta_cum[-1])\n", "    c.add_port(name=\"o2\", center=final_center, orientation=final_orientation, cross_section=cross_section)\n", "\n", "    return c\n", "\n", "# Example usage\n", "c = gvc_bend()\n", "c.show()"]}, {"cell_type": "code", "execution_count": null, "id": "a299b75c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 对比测试：正角度+负曲率 ===\n", "Grok版本运行错误: module 'numpy' has no attribute 'cumtrapz'\n", "\n", "改进版本端口信息:\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_17400\\3507220067.py:76: DeprecationWarning: `trapz` is deprecated. Use `trapezoid` instead, or one of the numerical integration functions in `scipy.integrate`.\n", "  avg_curvature = np.trapz(k_u, u)  # ∫k(u)du from 0 to 1\n"]}], "source": ["import numpy as np\n", "import gdsfactory as gf\n", "\n", "@gf.cell\n", "def gvc_bend_improved(\n", "    final_angle: float = 45.0,\n", "    final_curvature: float = -0.02,  # 1/μm，可以与angle符号不同\n", "    transition_type: str = \"smooth\",  # \"smooth\", \"linear\", \"s_curve\"\n", "    width: float = 0.5,\n", "    layer: tuple = (1, 0),\n", "    npoints: int = 401,\n", "    auto_length: bool = True,\n", "    target_length: float = None,\n", ") -> gf.Component:\n", "    \"\"\"改进版通用可变曲率弯曲波导（GVC Bend）\n", "    \n", "    相比grok版本的改进：\n", "    1. 端口正确对齐切线方向\n", "    2. 统一的数值积分，避免分段误差\n", "    3. 更灵活的参数组合（支持正角+负曲率）\n", "    4. 自动长度计算或手动指定\n", "    5. 多种过渡模式\n", "    \n", "    Args:\n", "        final_angle: 最终角度（度），正=左转，负=右转\n", "        final_curvature: 最终曲率（1/μm），可与angle符号独立\n", "        transition_type: 曲率过渡类型\n", "        width: 波导宽度\n", "        layer: 绘制层\n", "        npoints: 路径点数\n", "        auto_length: 是否自动计算长度\n", "        target_length: 手动指定长度（当auto_length=False时使用）\n", "    \"\"\"\n", "    \n", "    theta_target = np.deg2rad(final_angle)\n", "    k_final = final_curvature\n", "    \n", "    # 参数验证（更宽松）\n", "    if abs(theta_target) > np.pi:\n", "        raise ValueError(\"final_angle should be within ±180 degrees\")\n", "    \n", "    # 自动计算合适的长度或使用指定长度\n", "    if auto_length:\n", "        # 基于目标角度和曲率特性估算合理长度\n", "        if abs(k_final) > 1e-6:\n", "            # 有显著终曲率时，需要足够长度来过渡\n", "            base_length = abs(theta_target / (k_final * 0.5)) if k_final != 0 else 50\n", "            length = max(20, min(200, base_length))  # 限制在合理范围\n", "        else:\n", "            # 欧拉弯类型\n", "            length = max(30, abs(theta_target) * 30)\n", "    else:\n", "        length = target_length if target_length is not None else 50\n", "    \n", "    # 参数化变量\n", "    u = np.linspace(0, 1, npoints)\n", "    \n", "    # 根据过渡类型定义曲率分布 k(u)\n", "    if transition_type == \"linear\":\n", "        # 线性过渡：k(u) = k_final * u\n", "        k_u = k_final * u\n", "    elif transition_type == \"smooth\":\n", "        # 平滑过渡：k(u) = k_final * (3u² - 2u³)  [S型]\n", "        smooth_u = 3 * u**2 - 2 * u**3\n", "        k_u = k_final * smooth_u\n", "    elif transition_type == \"s_curve\":\n", "        # S曲线：先增后减再增，适合复杂轨迹\n", "        # k(u) = k_final * u + A * u * (1-u) * sin(π*u)\n", "        A = k_final * 0.5  # 中间振荡幅度\n", "        k_u = k_final * u + A * u * (1 - u) * np.sin(np.pi * u)\n", "    else:\n", "        raise ValueError(f\"Unknown transition_type: {transition_type}\")\n", "    \n", "    # 调整长度以匹配目标角度\n", "    # ∫k(u)du * length = theta_target\n", "    avg_curvature = np.trapz(k_u, u)  # ∫k(u)du from 0 to 1\n", "    if abs(avg_curvature) > 1e-9:\n", "        length_corrected = theta_target / avg_curvature\n", "        if auto_length:\n", "            length = abs(length_corrected)\n", "        else:\n", "            # 手动长度时，缩放曲率以匹配\n", "            k_u = k_u * (theta_target / (avg_curvature * length))\n", "    \n", "    # 弧长参数\n", "    s = u * length\n", "    \n", "    # 数值积分：计算角度 θ(s)\n", "    theta = np.zeros(npoints)\n", "    for i in range(1, npoints):\n", "        # 梯形积分\n", "        ds = s[i] - s[i-1]\n", "        theta[i] = theta[i-1] + 0.5 * (k_u[i] + k_u[i-1]) * ds\n", "    \n", "    # 计算位置 x(s), y(s)\n", "    cos_theta = np.cos(theta)\n", "    sin_theta = np.sin(theta)\n", "    \n", "    x = np.zeros(npoints)\n", "    y = np.zeros(npoints)\n", "    for i in range(1, npoints):\n", "        ds = s[i] - s[i-1]\n", "        x[i] = x[i-1] + 0.5 * (cos_theta[i] + cos_theta[i-1]) * ds\n", "        y[i] = y[i-1] + 0.5 * (sin_theta[i] + sin_theta[i-1]) * ds\n", "    \n", "    # 创建路径和组件\n", "    points = list(zip(x, y))\n", "    \n", "    # 使用适当的平滑半径\n", "    smooth_radius = max(0.1, length / npoints * 2)\n", "    path = gf.path.smooth(points, radius=smooth_radius)\n", "    \n", "    cross_section = gf.cross_section.cross_section(width=width, layer=layer)\n", "    \n", "    c = gf.Component()\n", "    waveguide_ref = c << path.extrude(cross_section)\n", "    \n", "    # 正确添加端口（沿切线方向）\n", "    # 输入端口：起点，方向180度（向右传播）\n", "    c.add_port(\n", "        name=\"o1\",\n", "        center=(x[0], y[0]),\n", "        width=width,\n", "        orientation=180.0,  # 输入方向\n", "        layer=layer\n", "    )\n", "    \n", "    # 输出端口：终点，沿最终切线方向\n", "    final_orientation_deg = float(np.rad2deg(theta[-1]))\n", "    c.add_port(\n", "        name=\"o2\",\n", "        center=(x[-1], y[-1]),\n", "        width=width,\n", "        orientation=final_orientation_deg,  # 沿切线，不是折线\n", "        layer=layer\n", "    )\n", "    \n", "    # 添加信息记录\n", "    c.info.update({\n", "        \"final_angle_target\": final_angle,\n", "        \"final_angle_actual\": final_orientation_deg,\n", "        \"final_curvature_target\": final_curvature,\n", "        \"final_curvature_actual\": float(k_u[-1]),\n", "        \"transition_type\": transition_type,\n", "        \"total_length\": float(length),\n", "        \"auto_length\": auto_length,\n", "    })\n", "    \n", "    return c\n", "\n", "# 对比测试：grok版本 vs 改进版本\n", "print(\"=== 对比测试：正角度+负曲率 ===\")\n", "\n", "# grok版本（如果能运行的话）\n", "try:\n", "    c_grok = gvc_bend(final_angle=45.0, final_curvature=-0.02, peak_curvature=0.1)\n", "    print(\"Grok版本端口信息:\")\n", "    c_grok.pprint_ports()\n", "    print(\"实际终角:\", np.rad2deg(np.arctan2(\n", "        c_grok.ports[\"o2\"].orientation_vector[1], \n", "        c_grok.ports[\"o2\"].orientation_vector[0]\n", "    )), \"度\")\n", "except Exception as e:\n", "    print(f\"Grok版本运行错误: {e}\")\n", "    c_grok = None\n", "\n", "print(\"\\n改进版本端口信息:\")\n", "c_improved = gvc_bend_improved(\n", "    final_angle=45.0, \n", "    final_curvature=-0.02,\n", "    transition_type=\"smooth\",\n", "    auto_length=True\n", ")\n", "c_improved.pprint_ports()\n", "print(\"组件信息:\", c_improved.info)\n", "\n", "# 显示结果\n", "if c_grok:\n", "    c_grok.plot()\n", "c_improved.plot()\n", "c_improved.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 5}