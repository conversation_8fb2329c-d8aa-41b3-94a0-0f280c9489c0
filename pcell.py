import sys
import logging
from pathlib import Path
import gdsfactory as gf
from gdsfactory.technology import LayerStack, LayerLevel, LogicalLayer
sys.path.append(str(Path(__file__).parent.parent))

from write_sparameters_lumerical_mode import write_sparameters_lumerical
# --- Configuration ---
LUMERICAL_API_PATH = "D:\\Program Files\\Lumerical\\v231\\api\\python"
#LUMERICAL_API_PATH = "C:\\Program Files\\ANSYS Inc\\v251\\Lumerical\\api\\python"
SIMULATION_DIR_NAME = "simulation_output"
FSP_FILE_NAME = "PCELL_FDTD.fsp"


@gf.cell
def pcell_simulation(
    offset: float = 40.0,
    radius: float = 50.0,
    r1:float=2,
    r2:float=2,
    w:float=2,
) :
    """
    由wg pcell加上slab，clad用于模拟
    """
    # Create a new, empty component to hold everything.
    c = gf.Component()
    # 这里是波导（1，0）
    #WG = gf.components.bends.bend_euler_s(layer=(1,0))
    #WG=bend_s_offset(offset=offset, radius=radius)
    #WG=gf.components.crossing_etched(width=0.8, r1=r1, r2=r2, w=w, L=3.5, layer_wg='WG')
    #xs = gf.cross_section.strip(width=1, layer=(1, 0))
    # WG=gf.components.straight(
    # length=10,  # Length in microns
    # cross_section=xs
    # )       
    section_inner = gf.cross_section.cross_section(
        width=1.5,
        offset=0,
        layer=(1, 0)
    )
    section_outer = gf.cross_section.cross_section(
        width=2,
        offset=0,
        layer=(1, 0)
    )

    # 创建基础环形耦合器
    coupler = gf.components.coupler_ring_bend(
        coupler_gap=0.5,
        radius=50,
        coupling_angle_coverage=20,
        length_x=0,
        cross_section_inner=section_inner,
        cross_section_outer=section_outer,
        bend_output="bend_euler_all_angle",
        straight="straight",
    )

    # 创建新的组件
    WG = gf.Component()

    # 添加耦合器
    coupler_ref = WG << coupler

    # 添加两个直波导
    s1 = WG << gf.get_component("straight", length=20, cross_section=section_outer)
    s2 = WG << gf.get_component("straight", length=20, cross_section=section_outer)

    # 连接直波导到耦合器的端口
    s1.connect("o1", coupler_ref.ports["o1"])
    s2.connect("o1", coupler_ref.ports["o4"])

    # 添加端口（新的端口位置）
    WG.add_port("o1", port=s1.ports["o2"])
    # WG.add_port("o2", port=coupler_ref.ports["o2"])
    WG.add_port("o3", port=coupler_ref.ports["o3"])
    # WG.add_port("o4", port=s2.ports["o2"])
    # Add the MMI component as a reference to our main component `c`.
    WG_ref = c.add_ref(WG)
    # Create a rectangle background with the same size as the MMI.
    # The .size attribute is a convenient way to get the (width, height) tuple.
    box = gf.components.rectangle(
        size=(WG_ref.xmax - WG_ref.xmin+3, WG_ref.ymax - WG_ref.ymin+6), layer=(3,0)
    )
    slab= gf.components.rectangle(
        size=(WG_ref.xmax - WG_ref.xmin+3, WG_ref.ymax - WG_ref.ymin+6), layer=(2,0)
    )
    clad=gf.components.rectangle(
        size=(WG_ref.xmax - WG_ref.xmin+3, WG_ref.ymax - WG_ref.ymin+6), layer=(5,0)
    )
    # Add the background rectangle as a reference to `c`.
    rect_ref = c.add_ref(box)
    slab_ref = c.add_ref(slab) 
    clad_ref = c.add_ref(clad)
    # Align the center of the rectangle with the center of the MMI.
    WG_ref.center = (0, 0)
    rect_ref.center = WG_ref.center
    slab_ref.center = WG_ref.center
    clad_ref.center = WG_ref.center
    # Add the optical ports from the MMI reference to the main component `c`.
    c.add_ports(WG_ref.ports)
    #c.add_port('o1',port=WG_ref.ports['o1'])
    #c.add_port('o2',port=WG_ref.ports['o3'])

    return c

def setup_logging(simulation_dir):
    """Set up logging configuration to save all output to a log file."""
    log_file = simulation_dir / "simulation.log"

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, mode='w', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)  # Also show on console
        ]
    )

    # Configure gdsfactory logger to use the same handlers
    gf_logger = logging.getLogger('gdsfactory')
    gf_logger.setLevel(logging.INFO)

    return logging.getLogger(__name__)

def setup_lumerical_api():
    """Checks for and appends the Lumerical API path to sys.path."""
    logger = logging.getLogger(__name__)
    if LUMERICAL_API_PATH not in sys.path:
        logger.info(f"Adding Lumerical API path: {LUMERICAL_API_PATH}")
        sys.path.append(LUMERICAL_API_PATH)
    else:
        logger.info("Lumerical API path already configured.")

def qc_layer_stack(
    wg_thickness: float = 0.35,
    slab_thickness: float = 0.3,
    clad_thickness: float = 2.0,
    box_thickness: float = 2,
    wg_material: str='LN',
    clad_material: str = 'sio2',
    sidewall_angle: float = 70.0
) -> LayerStack:
    logger = logging.getLogger(__name__)
    logger.info(f"Creating LayerStack with {sidewall_angle}-degree sidewall angle...")
    return LayerStack(
        #layer的编号和pcell对应
        layers={
            "WG": LayerLevel(
                layer=LogicalLayer(layer=(1, 0)),
                thickness=wg_thickness,
                zmin=slab_thickness,
                material=wg_material,
                sidewall_angle=sidewall_angle,
                mesh_order=2,
            ),
            "box": LayerLevel(
                layer=LogicalLayer(layer=(3, 0)),
                thickness=box_thickness,
                zmin=-box_thickness,
                material="sio2",
                mesh_order=3,
            ),
            "slab": LayerLevel(
                layer=LogicalLayer(layer=(2, 0)),
                thickness=slab_thickness,
                zmin=0,
                material=wg_material,
                mesh_order=2,
            ),
            "clad": LayerLevel(
                layer=LogicalLayer(layer=(5, 0)),
                thickness=clad_thickness,
                zmin=0,
                material='sio2',
                mesh_order=3,
            ),
        }
    )

def main():
    """
    Main function to create and configure the Lumerical FDTD simulation
    for a trapezoidal cross-section waveguide.
    """
    base_path = Path(__file__).parent.parent
    simulation_dir = base_path / SIMULATION_DIR_NAME
    simulation_dir.mkdir(exist_ok=True)

    # Set up logging first
    logger = setup_logging(simulation_dir)
    logger.info("--- Starting Lumerical FDTD Simulation Setup ---")

    setup_lumerical_api()

    try:
        import gplugins.lumerical as gl
        import lumapi
        logger.info("Successfully imported gplugins and lumapi.")
    except ImportError as e:
        logger.error(f"Error: Failed to import gplugins or lumapi. Ensure they are installed.")
        logger.error(f"   Details: {e}")
        sys.exit(1)

    fsp_filepath = simulation_dir / FSP_FILE_NAME
    gds_filepath = simulation_dir / f"{fsp_filepath.stem}.gds"

    logger.info(f"Output FSP file will be saved to: {fsp_filepath.resolve()}")
    logger.info(f"Output GDS file will be saved to: {gds_filepath.resolve()}")

    #gf.generic_tech.get_generic_pdk().activate()
    logger.info("gdsfactory PDK activated.")

    # 1. Define the LayerStack with the desired trapezoidal cross-section
    #ne在y轴方向
    LN_X = { "B1": [2.6734,2.9804,2.6734], "B2": [1.2290,0.5981,1.2290], "B3": [12.614,8.9543,12.614], "C1": [0.01764,0.02047,0.01764], "C2": [0.05914,0.0666,0.05914], "C3": [474.6,416.08,474.6]}
    #ne在z轴方向
    LN_Z = { "B1": [2.6734,2.6734,2.9804], "B2": [1.2290,1.2290,0.5981], "B3": [12.614,12.614,8.9543], "C1": [0.01764,0.01764,0.02047], "C2": [0.05914,0.05914,0.0666], "C3": [474.6,474.6,416.08]}
    material_name_to_lumerical={'LN_X':LN_X }
    layer_stack = qc_layer_stack(wg_material='LN_X')

    # 2. pcell
    # The cross-section itself is simple; the 3D geometry comes from the LayerStack
    pcell=pcell_simulation()

# 显示最终结果
    #pcell.show()


    #pcell.write_gds(gds_filepath)
    #print(f"Intermediate GDS file saved to: {gds_filepath.resolve()}")

    # 3. Generate the Lumerical FDTD project file
    logger.info("\nLaunching Lumerical session to generate FSP file...")
    logger.info("(This may take a moment, and a Lumerical window might open)")

    with lumapi.FDTD(hide=False) as session:
        logger.info("Lumerical session started.")
        write_sparameters_lumerical(
            component=pcell,
            layer_stack=layer_stack,
            material_name_to_lumerical=material_name_to_lumerical,
            session=session,
            run=False,
            output_dir=simulation_dir,  # Pass the output directory
            simulation_settings={
                "wavelength_start": 1.55,
                "wavelength_stop": 1.55,
                "wavelength_points": 1,
                "mesh_accuracy": 3,
                "frequency_dependent_profile": False,   #GPU计算,需要到write_sparameters_lumerical修改
            }
        )
        logger.info("Lumerical model configured successfully.")
        input("Press Enter to close the Lumerical GUI and finish...")
        session.save(str(fsp_filepath.resolve()))

    logger.info("\n--- Simulation Setup Complete ---")
    logger.info(f"Successfully generated Lumerical project file!")
    logger.info(f"   ->  File Path: {fsp_filepath.resolve()}")
    logger.info("You can now open this file in Lumerical FDTD to inspect the geometry.")

if __name__ == "__main__":
    main()
