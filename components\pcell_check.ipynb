{"cells": [{"cell_type": "code", "execution_count": 16, "id": "86a7a1ea", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> name </span>┃<span style=\"font-weight: bold\"> width </span>┃<span style=\"font-weight: bold\"> orientation </span>┃<span style=\"font-weight: bold\"> layer    </span>┃<span style=\"font-weight: bold\"> center              </span>┃<span style=\"font-weight: bold\"> port_type </span>┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 0.6   │ 180.0       │ WG (1/0) │ (-178.948, -32.702) │ optical   │\n", "│ o2   │ 0.6   │ 0.0         │ WG (1/0) │ (178.948, -32.702)  │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴─────────────────────┴───────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mname\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mwidth\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1morientation\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mlayer   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mcenter             \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mport_type\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 0.6   │ 180.0       │ WG (1/0) │ (-178.948, -32.702) │ optical   │\n", "│ o2   │ 0.6   │ 0.0         │ WG (1/0) │ (178.948, -32.702)  │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴─────────────────────┴───────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import gdsfactory as gf\n", "\n", "section_inner = gf.cross_section.cross_section(\n", "    width=1.5,\n", "    offset=0,\n", "    layer=(1, 0)\n", ")\n", "section_outer = gf.cross_section.cross_section(\n", "    width=0.6,\n", "    offset=0,\n", "    layer=(1, 0)\n", ")\n", "\n", "# 创建基础环形耦合器\n", "coupler = gf.components.ring_single_bend_coupler(\n", "    radius=100, gap=0.6, \n", "    coupling_angle_coverage=90, bend='bend_circular', \n", "    bend_output='bend_euler_all_angle', \n", "    length_x=0, length_y=0, \n", "    cross_section_inner=section_inner, \n", "    cross_section_outer=section_outer)\n", "\n", "# 创建新的组件\n", "c = gf.Component()\n", "\n", "# 添加耦合器\n", "coupler_ref = c << coupler\n", "\n", "# 添加两个直波导\n", "s1 = c << gf.get_component(\"straight\", length=100, cross_section=section_outer)\n", "s2 = c << gf.get_component(\"straight\", length=100, cross_section=section_outer)\n", "\n", "# 连接直波导到耦合器的端口\n", "s1.connect(\"o1\", coupler_ref.ports[\"o1\"])\n", "s2.connect(\"o1\", coupler_ref.ports[\"o2\"])\n", "\n", "# 添加端口（新的端口位置）\n", "c.add_port(\"o1\", port=s1.ports[\"o2\"])\n", "# c.add_port(\"o2\", port=coupler_ref.ports[\"o2\"])\n", "# c.add_port(\"o3\", port=coupler_ref.ports[\"o3\"])\n", "c.add_port(\"o2\", port=s2.ports[\"o2\"])\n", "\n", "c.pprint_ports()\n", "c.draw_ports()\n", "c.plot()\n", "c.show()"]}, {"cell_type": "code", "execution_count": 19, "id": "6bd5f0d3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 内置欧拉弯曲 ===\n", "内置欧拉弯曲端口信息:\n"]}, {"name": "stderr", "output_type": "stream", "text": ["d:\\python\\python3_12\\Lib\\site-packages\\cachetools\\_cached.py:173: UserWarning: bend_euler angle should be 90 or 180. Got 45. Use bend_euler_all_angle instead.\n", "  v = func(*args, **kwargs)\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> name </span>┃<span style=\"font-weight: bold\"> width </span>┃<span style=\"font-weight: bold\"> orientation </span>┃<span style=\"font-weight: bold\"> layer    </span>┃<span style=\"font-weight: bold\"> center                       </span>┃<span style=\"font-weight: bold\"> port_type </span>┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 0.5   │ 180.0       │ WG (1/0) │ (0.0, 0.0)                   │ optical   │\n", "│ o2   │ 0.5   │ 45.0        │ WG (1/0) │ (14.142, 5.8580000000000005) │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴──────────────────────────────┴───────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mname\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mwidth\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1morientation\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mlayer   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mcenter                      \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mport_type\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 0.5   │ 180.0       │ WG (1/0) │ (0.0, 0.0)                   │ optical   │\n", "│ o2   │ 0.5   │ 45.0        │ WG (1/0) │ (14.142, 5.8580000000000005) │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴──────────────────────────────┴───────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== 路径欧拉弯曲 ===\n", "路径欧拉弯曲端口信息:\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> name </span>┃<span style=\"font-weight: bold\"> width </span>┃<span style=\"font-weight: bold\"> orientation </span>┃<span style=\"font-weight: bold\"> layer    </span>┃<span style=\"font-weight: bold\"> center                                  </span>┃<span style=\"font-weight: bold\"> port_type </span>┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 0.5   │ 180.0       │ WG (1/0) │ (0.0, 0.0)                              │ optical   │\n", "│ o2   │ 0.5   │ 45.0        │ WG (1/0) │ (20.928068031846067, 8.668689613057447) │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴─────────────────────────────────────────┴───────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mname\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mwidth\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1morientation\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mlayer   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mcenter                                 \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mport_type\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 0.5   │ 180.0       │ WG (1/0) │ (0.0, 0.0)                              │ optical   │\n", "│ o2   │ 0.5   │ 45.0        │ WG (1/0) │ (20.928068031846067, 8.668689613057447) │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴─────────────────────────────────────────┴───────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import gdsfactory as gf\n", "import numpy as np\n", "\n", "def custom_euler_bend(\n", "    angle: float = 90,  # 最终角度（度）\n", "    radius: float = 10,  # 最终曲率半径\n", "    length: float = 50,  # 弯曲段长度\n", "    width: float = 0.5,  # 波导宽度\n", "    layer: tuple = (1, 0),  # 层\n", "    n_points: int = 100  # 路径点数\n", "):\n", "    \"\"\"\n", "    创建自定义欧拉弯曲波导\n", "    - 传播方向：从左往右\n", "    - 曲率：使用欧拉螺旋，从0平滑变化到1/radius\n", "    - 最终输出特定角度的波导\n", "    \"\"\"\n", "    c = gf.Component()\n", "    \n", "    # 转换角度为弧度\n", "    angle_rad = np.radians(angle)\n", "    \n", "    # 创建路径参数\n", "    t = np.linspace(0, 1, n_points)\n", "    \n", "    # 起始点在原点，方向向右（0度）\n", "    x0, y0 = 0, 0\n", "    \n", "    # 欧拉螺旋的曲率变化：线性从0变化到最大曲率\n", "    max_curvature = 1.0 / radius\n", "    curvature = max_curvature * t  # 线性变化\n", "    \n", "    # 计算弧长参数\n", "    ds = length / (n_points - 1)\n", "    \n", "    # 使用欧拉螺旋公式计算角度（曲率的积分）\n", "    angles = np.zeros(n_points)\n", "    for i in range(1, n_points):\n", "        # 欧拉螺旋：角度 = ∫κ(s)ds，其中κ(s) = κ_max * s/L\n", "        # 积分结果：θ = κ_max * s²/(2*L)\n", "        s = i * ds\n", "        angles[i] = max_curvature * s**2 / (2 * length)\n", "    \n", "    # 缩放角度以达到目标最终角度\n", "    if angles[-1] != 0:\n", "        scale_factor = angle_rad / angles[-1]\n", "        angles = angles * scale_factor\n", "    \n", "    # 计算路径点坐标\n", "    x = np.zeros(n_points)\n", "    y = np.zeros(n_points)\n", "    x[0] = x0\n", "    y[0] = y0\n", "    \n", "    for i in range(1, n_points):\n", "        # 当前方向角度\n", "        current_angle = angles[i]\n", "        # 步长在当前方向上的分量\n", "        x[i] = x[i-1] + ds * np.cos(current_angle)\n", "        y[i] = y[i-1] + ds * np.sin(current_angle)\n", "    \n", "    # 创建路径点\n", "    points = list(zip(x, y))\n", "    \n", "    # 使用gdsfactory的路径功能创建平滑路径\n", "    path = gf.path.smooth(points, radius=0.5)\n", "    \n", "    # 创建截面\n", "    cross_section = gf.cross_section.cross_section(width=width, layer=layer)\n", "    \n", "    # 创建波导\n", "    waveguide = path.extrude(cross_section)\n", "    c.add_ref(waveguide)\n", "    \n", "    # 添加端口\n", "    # 起始端口（左向右，输入端）\n", "    c.add_port(\n", "        name=\"o1\",\n", "        center=(x[0], y[0]),\n", "        width=width,\n", "        orientation=180,  # 向右传播，端口朝左\n", "        layer=layer\n", "    )\n", "    \n", "    # 结束端口（输出端）\n", "    c.add_port(\n", "        name=\"o2\", \n", "        center=(x[-1], y[-1]),\n", "        width=width,\n", "        orientation=np.degrees(angles[-1]),  # 最终角度方向\n", "        layer=layer\n", "    )\n", "    \n", "    return c\n", "\n", "# 更简单的方法：直接使用gdsfactory的内置欧拉弯曲\n", "def simple_euler_bend(\n", "    angle: float = 90,\n", "    radius: float = 10,\n", "    width: float = 0.5,\n", "    layer: tuple = (1, 0)\n", "):\n", "    \"\"\"\n", "    使用gdsfactory内置的欧拉弯曲\n", "    \"\"\"\n", "    # 创建截面\n", "    cross_section = gf.cross_section.cross_section(width=width, layer=layer)\n", "    \n", "    # 创建欧拉弯曲\n", "    bend = gf.components.bend_euler(\n", "        angle=angle,\n", "        radius=radius,\n", "        cross_section=cross_section\n", "    )\n", "    \n", "    return bend\n", "\n", "# 最简单的方法：使用路径创建欧拉弯曲\n", "def path_euler_bend(\n", "    angle: float = 90,\n", "    radius: float = 10,\n", "    width: float = 0.5,\n", "    layer: tuple = (1, 0)\n", "):\n", "    \"\"\"\n", "    使用路径方法创建欧拉弯曲\n", "    \"\"\"\n", "    c = gf.Component()\n", "    \n", "    # 创建欧拉路径\n", "    path = gf.path.euler(radius=radius, angle=angle)\n", "    \n", "    # 创建截面\n", "    cross_section = gf.cross_section.cross_section(width=width, layer=layer)\n", "    \n", "    # 挤出波导\n", "    waveguide_ref = c << path.extrude(cross_section)\n", "    \n", "    # 添加端口\n", "    c.add_ports(waveguide_ref.ports)\n", "    \n", "    return c\n", "\n", "# 测试不同的欧拉弯曲方法\n", "print(\"=== 内置欧拉弯曲 ===\")\n", "builtin_bend = simple_euler_bend(\n", "    angle=45,\n", "    radius=20,\n", "    width=0.5,\n", "    layer=(1, 0)\n", ")\n", "\n", "print(\"内置欧拉弯曲端口信息:\")\n", "builtin_bend.pprint_ports()\n", "builtin_bend.plot()\n", "\n", "print(\"\\n=== 路径欧拉弯曲 ===\")\n", "path_bend = path_euler_bend(\n", "    angle=45,\n", "    radius=20,\n", "    width=0.5,\n", "    layer=(1, 0)\n", ")\n", "\n", "print(\"路径欧拉弯曲端口信息:\")\n", "path_bend.pprint_ports()\n", "path_bend.plot()\n", "\n", "# 显示组件\n", "builtin_bend.show()\n", "path_bend.show()"]}, {"cell_type": "code", "execution_count": null, "id": "ed255a0b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 21, "id": "5469baed", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== euler_bend_lr: +35°（左转） ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["d:\\python\\python3_12\\Lib\\site-packages\\cachetools\\_cached.py:173: UserWarning: bend_euler angle should be 90 or 180. Got 35. Use bend_euler_all_angle instead.\n", "  v = func(*args, **kwargs)\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> name </span>┃<span style=\"font-weight: bold\"> width </span>┃<span style=\"font-weight: bold\"> orientation </span>┃<span style=\"font-weight: bold\"> layer    </span>┃<span style=\"font-weight: bold\"> center          </span>┃<span style=\"font-weight: bold\"> port_type </span>┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 0.5   │ 180.0       │ WG (1/0) │ (0.0, 0.0)      │ optical   │\n", "│ o2   │ 0.5   │ 35.0        │ WG (1/0) │ (11.472, 3.617) │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴─────────────────┴───────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mname\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mwidth\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1morientation\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mlayer   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mcenter         \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mport_type\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 0.5   │ 180.0       │ WG (1/0) │ (0.0, 0.0)      │ optical   │\n", "│ o2   │ 0.5   │ 35.0        │ WG (1/0) │ (11.472, 3.617) │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴─────────────────┴───────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== euler_bend_lr: -30°（右转） ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["d:\\python\\python3_12\\Lib\\site-packages\\cachetools\\_cached.py:173: UserWarning: bend_euler angle should be 90 or 180. Got -30. Use bend_euler_all_angle instead.\n", "  v = func(*args, **kwargs)\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> name </span>┃<span style=\"font-weight: bold\"> width </span>┃<span style=\"font-weight: bold\"> orientation </span>┃<span style=\"font-weight: bold\"> layer    </span>┃<span style=\"font-weight: bold\"> center                      </span>┃<span style=\"font-weight: bold\"> port_type </span>┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 0.5   │ 180.0       │ WG (1/0) │ (0.0, 0.0)                  │ optical   │\n", "│ o2   │ 0.5   │ 330.0       │ WG (1/0) │ (10.0, -2.6790000000000003) │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴─────────────────────────────┴───────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mname\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mwidth\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1morientation\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mlayer   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mcenter                     \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mport_type\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 0.5   │ 180.0       │ WG (1/0) │ (0.0, 0.0)                  │ optical   │\n", "│ o2   │ 0.5   │ 330.0       │ WG (1/0) │ (10.0, -2.6790000000000003) │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴─────────────────────────────┴───────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import gdsfactory as gf\n", "\n", "# 更稳健：使用 bend_euler 支持任意角度（当前API兼容）\n", "# 规范：\n", "# - 传播方向：左→右\n", "# - 角度符号：正角=左转（逆时针），负角=右转（顺时针）\n", "# - 起点方向：朝右（输入端口 orientation=180）\n", "# - 终点方向：初始朝右基础上再旋转 angle 度\n", "\n", "def euler_bend_lr(\n", "    angle: float,           # 终端转角（度），正左转/负右转\n", "    radius: float = 20.0,   # 欧拉最小半径\n", "    width: float = 0.5,\n", "    layer: tuple = (1, 0),\n", "):\n", "    # 使用 gdsfactory 的欧拉弯曲器件（兼容任意角度）\n", "    xs = gf.cross_section.cross_section(width=width, layer=layer)\n", "    bend = gf.components.bend_euler(angle=angle, radius=radius, cross_section=xs)\n", "\n", "    # 封装以固定端口命名与朝向规范\n", "    c = gf.Component()\n", "    r = c << bend\n", "\n", "    # 将输入端口重命名为 o1，输出端口为 o2\n", "    c.add_port(\"o1\", port=r.ports[\"o1\"])  # 通常在左侧，朝向180（起始方向朝右传播）\n", "    c.add_port(\"o2\", port=r.ports[\"o2\"])  # 输出口相对起始方向旋转 angle 度\n", "\n", "    return c\n", "\n", "# Demo：正角左转 35°，负角右转 -30°\n", "print(\"=== euler_bend_lr: +35°（左转） ===\")\n", "bend_pos = euler_bend_lr(angle=35, radius=20, width=0.5, layer=(1, 0))\n", "bend_pos.pprint_ports()\n", "bend_pos.plot()\n", "\n", "print(\"\\n=== euler_bend_lr: -30°（右转） ===\")\n", "bend_neg = euler_bend_lr(angle=-30, radius=20, width=0.5, layer=(1, 0))\n", "bend_neg.pprint_ports()\n", "bend_neg.plot()\n", "\n", "bend_pos.show()\n", "bend_neg.show()"]}, {"cell_type": "code", "execution_count": null, "id": "6f2a056a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "id": "1670ee86", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== GVC Bend Demo: angle=+45°, k_final=-0.02 1/um ===\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mKeyboardInterrupt\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 136\u001b[39m\n\u001b[32m    134\u001b[39m \u001b[38;5;66;03m# 演示：正角 + 负曲率（例如 angle=+45°, k_final=-0.02 1/um）\u001b[39;00m\n\u001b[32m    135\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33m=== GVC Bend Demo: angle=+45°, k_final=-0.02 1/um ===\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m--> \u001b[39m\u001b[32m136\u001b[39m comp_gvc = \u001b[43mgvc_bend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mangle_deg\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m45.0\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mk_final\u001b[49m\u001b[43m=\u001b[49m\u001b[43m-\u001b[49m\u001b[32;43m0.02\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mwidth\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m0.5\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlayer\u001b[49m\u001b[43m=\u001b[49m\u001b[43m(\u001b[49m\u001b[32;43m1\u001b[39;49m\u001b[43m,\u001b[49m\u001b[32;43m0\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mn_points\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m801\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m    137\u001b[39m comp_gvc.pprint_ports()\n\u001b[32m    138\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33minfo:\u001b[39m\u001b[33m\"\u001b[39m, comp_gvc.info)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 98\u001b[39m, in \u001b[36mgvc_bend\u001b[39m\u001b[34m(angle_deg, k_final, width, layer, n_points, shape_c, smooth_factor)\u001b[39m\n\u001b[32m     95\u001b[39m path = gf.path.smooth(pts, radius=smooth_r)\n\u001b[32m     97\u001b[39m xs = gf.cross_section.cross_section(width=width, layer=layer)\n\u001b[32m---> \u001b[39m\u001b[32m98\u001b[39m wg = \u001b[43mpath\u001b[49m\u001b[43m.\u001b[49m\u001b[43mextrude\u001b[49m\u001b[43m(\u001b[49m\u001b[43mxs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    100\u001b[39m comp = gf.Component()\n\u001b[32m    101\u001b[39m ref = comp << wg\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\python\\python3_12\\Lib\\site-packages\\gdsfactory\\path.py:551\u001b[39m, in \u001b[36mPath.extrude\u001b[39m\u001b[34m(self, cross_section, layer, width, simplify, all_angle)\u001b[39m\n\u001b[32m    519\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mextrude\u001b[39m(\n\u001b[32m    520\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m    521\u001b[39m     cross_section: CrossSectionSpec | \u001b[38;5;28;01mNone\u001b[39;00m = \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m   (...)\u001b[39m\u001b[32m    525\u001b[39m     all_angle: \u001b[38;5;28mbool\u001b[39m = \u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[32m    526\u001b[39m ) -> AnyComponent:\n\u001b[32m    527\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Returns Component by extruding a Path with a CrossSection.\u001b[39;00m\n\u001b[32m    528\u001b[39m \n\u001b[32m    529\u001b[39m \u001b[33;03m    A path can be extruded using any CrossSection returning a Component\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    549\u001b[39m \u001b[33;03m        c.plot()\u001b[39;00m\n\u001b[32m    550\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m551\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mextrude\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    552\u001b[39m \u001b[43m        \u001b[49m\u001b[43mp\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m    553\u001b[39m \u001b[43m        \u001b[49m\u001b[43mcross_section\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcross_section\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    554\u001b[39m \u001b[43m        \u001b[49m\u001b[43mlayer\u001b[49m\u001b[43m=\u001b[49m\u001b[43mlayer\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    555\u001b[39m \u001b[43m        \u001b[49m\u001b[43mwidth\u001b[49m\u001b[43m=\u001b[49m\u001b[43mwidth\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    556\u001b[39m \u001b[43m        \u001b[49m\u001b[43msimplify\u001b[49m\u001b[43m=\u001b[49m\u001b[43msimplify\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    557\u001b[39m \u001b[43m        \u001b[49m\u001b[43mall_angle\u001b[49m\u001b[43m=\u001b[49m\u001b[43mall_angle\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    558\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\python\\python3_12\\Lib\\site-packages\\gdsfactory\\path.py:1107\u001b[39m, in \u001b[36mextrude\u001b[39m\u001b[34m(p, cross_section, layer, width, simplify, all_angle)\u001b[39m\n\u001b[32m   1104\u001b[39m points_poly = np.concatenate([points1, points2[::-\u001b[32m1\u001b[39m, :]])\n\u001b[32m   1106\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m hidden \u001b[38;5;129;01mand\u001b[39;00m p_sec.length() > \u001b[32m1e-3\u001b[39m:\n\u001b[32m-> \u001b[39m\u001b[32m1107\u001b[39m     \u001b[43mc\u001b[49m\u001b[43m.\u001b[49m\u001b[43madd_polygon\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpoints_poly\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlayer\u001b[49m\u001b[43m=\u001b[49m\u001b[43mlayer\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1109\u001b[39m \u001b[38;5;66;03m# Add port_names if they were specified\u001b[39;00m\n\u001b[32m   1110\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m port_names[\u001b[32m0\u001b[39m]:\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\python\\python3_12\\Lib\\site-packages\\gdsfactory\\component.py:931\u001b[39m, in \u001b[36mComponent.add_polygon\u001b[39m\u001b[34m(self, points, layer)\u001b[39m\n\u001b[32m    927\u001b[39m     \u001b[38;5;28;01m<PERSON>se\u001b[39;00m <PERSON><PERSON><PERSON><PERSON><PERSON>(\u001b[38;5;28mself\u001b[39m)\n\u001b[32m    929\u001b[39m _layer = get_layer(layer)\n\u001b[32m--> \u001b[39m\u001b[32m931\u001b[39m polygon = \u001b[43mpoints_to_polygon\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpoints\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    933\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m.kdb_cell.shapes(_layer).insert(polygon)\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\python\\python3_12\\Lib\\site-packages\\gdsfactory\\component.py:77\u001b[39m, in \u001b[36mpoints_to_polygon\u001b[39m\u001b[34m(points)\u001b[39m\n\u001b[32m     75\u001b[39m     points = ensure_tuple_of_tuples(points)\n\u001b[32m     76\u001b[39m     polygon = kdb.DPolygon()\n\u001b[32m---> \u001b[39m\u001b[32m77\u001b[39m     polygon.assign_hull(\u001b[43mto_kdb_dpoints\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpoints\u001b[49m\u001b[43m)\u001b[49m)\n\u001b[32m     78\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(\n\u001b[32m     79\u001b[39m     points, kdb.Polygon | kdb.DPolygon | kdb.DSimplePolygon | kdb.Region\n\u001b[32m     80\u001b[39m ):\n\u001b[32m     81\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m points\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\python\\python3_12\\Lib\\site-packages\\gdsfactory\\utils.py:17\u001b[39m, in \u001b[36mto_kdb_dpoints\u001b[39m\u001b[34m(points)\u001b[39m\n\u001b[32m     13\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mto_kdb_dpoints\u001b[39m(\n\u001b[32m     14\u001b[39m     points: \u001b[33m\"\u001b[39m\u001b[33mSequence[Coordinate | kdb.Point | kdb.DPoint]\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m     15\u001b[39m ) -> \u001b[38;5;28mlist\u001b[39m[kdb.DPoint]:\n\u001b[32m     16\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m [\n\u001b[32m---> \u001b[39m\u001b[32m17\u001b[39m         point\n\u001b[32m     18\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(point, kdb.DPoint)\n\u001b[32m     19\u001b[39m         \u001b[38;5;28;01melse\u001b[39;00m (\n\u001b[32m     20\u001b[39m             kdb.DPoint(point[\u001b[32m0\u001b[39m], point[\u001b[32m1\u001b[39m])\n\u001b[32m     21\u001b[39m             \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(point, \u001b[38;5;28mtuple\u001b[39m)\n\u001b[32m     22\u001b[39m             \u001b[38;5;28;01melse\u001b[39;00m kdb.DPoint(point.x, point.y)\n\u001b[32m     23\u001b[39m         )\n\u001b[32m     24\u001b[39m         \u001b[38;5;28;01mfor\u001b[39;00m point \u001b[38;5;129;01min\u001b[39;00m points\n\u001b[32m     25\u001b[39m     ]\n", "\u001b[31mKeyboardInterrupt\u001b[39m: "]}], "source": ["import gdsfactory as gf\n", "import numpy as np\n", "\n", "# GVC Bend: 通用可变曲率弯曲波导\n", "# 约束：\n", "# - 起点：(0,0)\n", "# - 初始方向：沿 +X（0 rad）\n", "# - 起始曲率：k(0)=0\n", "# 控制：\n", "# - 最终角度 angle_deg（度，正=左转/逆时针，负=右转/顺时针）\n", "# - 最终曲率 k_final（1/um，可与角度符号独立；例如 angle=+45°, k_final<0）\n", "# - 过程形状参数 shape_c（1/um），控制曲率分布：k(u)=k_final*u + shape_c*u*(1-u)\n", "#   若 shape_c=None，将自动选择一个值，以保证总长度为正并数值稳定。\n", "\n", "def gvc_bend(\n", "    angle_deg: float,\n", "    k_final: float,\n", "    width: float = 0.5,\n", "    layer: tuple[int, int] = (1, 0),\n", "    n_points: int = 801,  # 需为奇数以便端点对称\n", "    shape_c: float | None = None,\n", "    smooth_factor: float = 0.25,  # 用于 gf.path.smooth 的局部半径比例（相对于步长）\n", "):\n", "    \"\"\"\n", "    返回满足指定终角与终曲率的弯曲波导组件（左→右传播）。\n", "    - 起点：(0,0)，初始方向 0 度，k(0)=0\n", "    - 终点：切向角 angle_deg，曲率 k_final（1/um）\n", "    - 过程：曲率按 u∈[0,1] 的多项式 k(u)=k_final*u + shape_c*u*(1-u)\n", "\n", "    注意：\n", "    - 当 angle_deg 与 k_final 符号相反时，纯欧拉（线性曲率）无法满足；通过 shape_c 调整曲率平均值以实现正长度。\n", "    - 若未提供 shape_c，将根据 angle/k_final 自动选择一个稳健的值。\n", "    \"\"\"\n", "    assert n_points >= 5, \"n_points 太小\"\n", "    if n_points % 2 == 0:\n", "        n_points += 1\n", "\n", "    theta = np.deg2rad(angle_deg)  # 目标终角（弧度）\n", "\n", "    # 自动选择 shape_c（若未提供），以保证长度正且不过分夸张\n", "    if shape_c is None:\n", "        # 目标积分 I = ∫_0^1 k(u) du = k_final/2 + shape_c/6\n", "        # 为保证 L = theta / I > 0，使 I 与 theta 同号；给 shape_c 一个与 theta 同号的偏置量\n", "        # 选择与 |k_final| 同量级的偏置，避免长度过大/过小\n", "        if theta == 0:\n", "            # theta=0 时，给定一个温和的 I 以生成近似S弯（终角~0但终曲率=k_final）\n", "            shape_c = np.sign(k_final) * 0.5 * max(abs(k_final), 1e-3)\n", "        else:\n", "            if theta * k_final > 0:\n", "                shape_c = 0.0  # 线性曲率已可满足\n", "            else:\n", "                shape_c = np.sign(theta) * 1.5 * max(abs(k_final), 1e-3)\n", "\n", "    # 曲率平均值 I，进而确定总弧长 L\n", "    I = (k_final / 2.0) + (shape_c / 6.0)\n", "    if abs(I) < 1e-9:\n", "        # 远离奇点\n", "        I = np.sign(theta) * 1e-6 if theta != 0 else 1e-6\n", "    L = theta / I if I != 0 else 1.0\n", "\n", "    # 确保正长度：若出现负值，微调 shape_c\n", "    if L <= 0:\n", "        bump = np.sign(theta) * max(abs(k_final), 1e-3)\n", "        shape_c += bump\n", "        I = (k_final / 2.0) + (shape_c / 6.0)\n", "        L = theta / I if I != 0 else 1.0\n", "        if L <= 0:\n", "            # 再次兜底\n", "            shape_c += 2 * bump\n", "            I = (k_final / 2.0) + (shape_c / 6.0)\n", "            L = theta / I if I != 0 else 1.0\n", "\n", "    # 离散化并构造曲率序列 k(u)\n", "    u = np.linspace(0.0, 1.0, n_points)\n", "    k_u = (k_final * u) + (shape_c * u * (1.0 - u))  # 1/um\n", "\n", "    # 数值积分：phi'(s)=k(s)，x'(s)=cos(phi)，y'(s)=sin(phi)\n", "    ds = L / (n_points - 1)\n", "    phi = np.zeros(n_points)\n", "    for i in range(1, n_points):\n", "        phi[i] = phi[i-1] + 0.5 * (k_u[i] + k_u[i-1]) * ds\n", "\n", "    cos_phi = np.cos(phi)\n", "    sin_phi = np.sin(phi)\n", "    x = np.zeros(n_points)\n", "    y = np.zeros(n_points)\n", "    for i in range(1, n_points):\n", "        x[i] = x[i-1] + 0.5 * (cos_phi[i] + cos_phi[i-1]) * ds\n", "        y[i] = y[i-1] + 0.5 * (sin_phi[i] + sin_phi[i-1]) * ds\n", "\n", "    # 使用 gdsfactory 的 path.smooth 生成可挤出的路径\n", "    step = L / (n_points - 1)\n", "    smooth_r = max(step * smooth_factor, 1e-3)\n", "    pts = list(zip(x, y))\n", "    path = gf.path.smooth(pts, radius=smooth_r)\n", "\n", "    xs = gf.cross_section.cross_section(width=width, layer=layer)\n", "    wg = path.extrude(xs)\n", "\n", "    comp = gf.Component()\n", "    ref = comp << wg\n", "\n", "    # 端口：\n", "    # - 输入 o1：起点，传播方向左→右，因此端口朝向为 180°（入射口朝左）\n", "    # - 输出 o2：终点，端口朝向为实际终角（度）\n", "    comp.add_port(\n", "        name=\"o1\",\n", "        center=(x[0], y[0]),\n", "        width=width,\n", "        layer=layer,\n", "        orientation=180.0,\n", "    )\n", "    comp.add_port(\n", "        name=\"o2\",\n", "        center=(x[-1], y[-1]),\n", "        width=width,\n", "        layer=layer,\n", "        orientation=float(np.rad2deg(phi[-1])),\n", "    )\n", "\n", "    # 在 info 中记录参数与数值估计结果\n", "    comp.info.update({\n", "        \"angle_target_deg\": float(angle_deg),\n", "        \"angle_actual_deg\": float(np.rad2deg(phi[-1])),\n", "        \"k_final_target\": float(k_final),\n", "        \"k_profile_shape_c\": float(shape_c),\n", "        \"length_um\": float(L),\n", "        \"avg_curvature_I\": float(I),\n", "        \"n_points\": int(n_points),\n", "    })\n", "\n", "    return comp\n", "\n", "# 演示：正角 + 负曲率（例如 angle=+45°, k_final=-0.02 1/um）\n", "print(\"=== GVC Bend Demo: angle=+45°, k_final=-0.02 1/um ===\")\n", "comp_gvc = gvc_bend(angle_deg=45.0, k_final=-0.02, width=0.5, layer=(1,0), n_points=801)\n", "comp_gvc.pprint_ports()\n", "print(\"info:\", comp_gvc.info)\n", "comp_gvc.plot()\n", "comp_gvc.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 5}