import gdsfactory as gf

section_inner = gf.cross_section.cross_section(
    width=1.5,
    offset=0,
    layer=(1, 0)
)
section_outer = gf.cross_section.cross_section(
    width=0.6,
    offset=0,
    layer=(1, 0)
)

# 创建基础环形耦合器
coupler = gf.components.ring_single_bend_coupler(
    radius=100, gap=0.6, 
    coupling_angle_coverage=90, bend='bend_circular', 
    bend_output='bend_euler_all_angle', 
    length_x=0, length_y=0, 
    cross_section_inner=section_inner, 
    cross_section_outer=section_outer)

# 创建新的组件
c = gf.Component()

# 添加耦合器
coupler_ref = c << coupler

# 添加两个直波导
s1 = c << gf.get_component("straight", length=100, cross_section=section_outer)
s2 = c << gf.get_component("straight", length=100, cross_section=section_outer)

# 连接直波导到耦合器的端口
s1.connect("o1", coupler_ref.ports["o1"])
s2.connect("o1", coupler_ref.ports["o2"])

# 添加端口（新的端口位置）
c.add_port("o1", port=s1.ports["o2"])
# c.add_port("o2", port=coupler_ref.ports["o2"])
# c.add_port("o3", port=coupler_ref.ports["o3"])
c.add_port("o2", port=s2.ports["o2"])

c.pprint_ports()
c.draw_ports()
c.plot()
c.show()

import gdsfactory as gf
import numpy as np

def custom_euler_bend(
    angle: float = 90,  # 最终角度（度）
    radius: float = 10,  # 最终曲率半径
    length: float = 50,  # 弯曲段长度
    width: float = 0.5,  # 波导宽度
    layer: tuple = (1, 0),  # 层
    n_points: int = 100  # 路径点数
):
    """
    创建自定义欧拉弯曲波导
    - 传播方向：从左往右
    - 曲率：使用欧拉螺旋，从0平滑变化到1/radius
    - 最终输出特定角度的波导
    """
    c = gf.Component()
    
    # 转换角度为弧度
    angle_rad = np.radians(angle)
    
    # 创建路径参数
    t = np.linspace(0, 1, n_points)
    
    # 起始点在原点，方向向右（0度）
    x0, y0 = 0, 0
    
    # 欧拉螺旋的曲率变化：线性从0变化到最大曲率
    max_curvature = 1.0 / radius
    curvature = max_curvature * t  # 线性变化
    
    # 计算弧长参数
    ds = length / (n_points - 1)
    
    # 使用欧拉螺旋公式计算角度（曲率的积分）
    angles = np.zeros(n_points)
    for i in range(1, n_points):
        # 欧拉螺旋：角度 = ∫κ(s)ds，其中κ(s) = κ_max * s/L
        # 积分结果：θ = κ_max * s²/(2*L)
        s = i * ds
        angles[i] = max_curvature * s**2 / (2 * length)
    
    # 缩放角度以达到目标最终角度
    if angles[-1] != 0:
        scale_factor = angle_rad / angles[-1]
        angles = angles * scale_factor
    
    # 计算路径点坐标
    x = np.zeros(n_points)
    y = np.zeros(n_points)
    x[0] = x0
    y[0] = y0
    
    for i in range(1, n_points):
        # 当前方向角度
        current_angle = angles[i]
        # 步长在当前方向上的分量
        x[i] = x[i-1] + ds * np.cos(current_angle)
        y[i] = y[i-1] + ds * np.sin(current_angle)
    
    # 创建路径点
    points = list(zip(x, y))
    
    # 使用gdsfactory的路径功能创建平滑路径
    path = gf.path.smooth(points, radius=0.5)
    
    # 创建截面
    cross_section = gf.cross_section.cross_section(width=width, layer=layer)
    
    # 创建波导
    waveguide = path.extrude(cross_section)
    c.add_ref(waveguide)
    
    # 添加端口
    # 起始端口（左向右，输入端）
    c.add_port(
        name="o1",
        center=(x[0], y[0]),
        width=width,
        orientation=180,  # 向右传播，端口朝左
        layer=layer
    )
    
    # 结束端口（输出端）
    c.add_port(
        name="o2", 
        center=(x[-1], y[-1]),
        width=width,
        orientation=np.degrees(angles[-1]),  # 最终角度方向
        layer=layer
    )
    
    return c

# 更简单的方法：直接使用gdsfactory的内置欧拉弯曲
def simple_euler_bend(
    angle: float = 90,
    radius: float = 10,
    width: float = 0.5,
    layer: tuple = (1, 0)
):
    """
    使用gdsfactory内置的欧拉弯曲
    """
    # 创建截面
    cross_section = gf.cross_section.cross_section(width=width, layer=layer)
    
    # 创建欧拉弯曲
    bend = gf.components.bend_euler(
        angle=angle,
        radius=radius,
        cross_section=cross_section
    )
    
    return bend

# 最简单的方法：使用路径创建欧拉弯曲
def path_euler_bend(
    angle: float = 90,
    radius: float = 10,
    width: float = 0.5,
    layer: tuple = (1, 0)
):
    """
    使用路径方法创建欧拉弯曲
    """
    c = gf.Component()
    
    # 创建欧拉路径
    path = gf.path.euler(radius=radius, angle=angle)
    
    # 创建截面
    cross_section = gf.cross_section.cross_section(width=width, layer=layer)
    
    # 挤出波导
    waveguide_ref = c << path.extrude(cross_section)
    
    # 添加端口
    c.add_ports(waveguide_ref.ports)
    
    return c

# 测试不同的欧拉弯曲方法
print("=== 内置欧拉弯曲 ===")
builtin_bend = simple_euler_bend(
    angle=45,
    radius=20,
    width=0.5,
    layer=(1, 0)
)

print("内置欧拉弯曲端口信息:")
builtin_bend.pprint_ports()
builtin_bend.plot()

print("\n=== 路径欧拉弯曲 ===")
path_bend = path_euler_bend(
    angle=45,
    radius=20,
    width=0.5,
    layer=(1, 0)
)

print("路径欧拉弯曲端口信息:")
path_bend.pprint_ports()
path_bend.plot()

# 显示组件
builtin_bend.show()
path_bend.show()



import gdsfactory as gf

# 更稳健：使用 bend_euler 支持任意角度（当前API兼容）
# 规范：
# - 传播方向：左→右
# - 角度符号：正角=左转（逆时针），负角=右转（顺时针）
# - 起点方向：朝右（输入端口 orientation=180）
# - 终点方向：初始朝右基础上再旋转 angle 度

def euler_bend_lr(
    angle: float,           # 终端转角（度），正左转/负右转
    radius: float = 20.0,   # 欧拉最小半径
    width: float = 0.5,
    layer: tuple = (1, 0),
):
    # 使用 gdsfactory 的欧拉弯曲器件（兼容任意角度）
    xs = gf.cross_section.cross_section(width=width, layer=layer)
    bend = gf.components.bend_euler(angle=angle, radius=radius, cross_section=xs)

    # 封装以固定端口命名与朝向规范
    c = gf.Component()
    r = c << bend

    # 将输入端口重命名为 o1，输出端口为 o2
    c.add_port("o1", port=r.ports["o1"])  # 通常在左侧，朝向180（起始方向朝右传播）
    c.add_port("o2", port=r.ports["o2"])  # 输出口相对起始方向旋转 angle 度

    return c

# Demo：正角左转 35°，负角右转 -30°
print("=== euler_bend_lr: +35°（左转） ===")
bend_pos = euler_bend_lr(angle=35, radius=20, width=0.5, layer=(1, 0))
bend_pos.pprint_ports()
bend_pos.plot()

print("\n=== euler_bend_lr: -30°（右转） ===")
bend_neg = euler_bend_lr(angle=-30, radius=20, width=0.5, layer=(1, 0))
bend_neg.pprint_ports()
bend_neg.plot()

bend_pos.show()
bend_neg.show()

